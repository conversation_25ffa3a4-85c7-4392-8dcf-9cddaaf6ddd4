<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="9.00"
	Name="EraserUI"
	ProjectGUID="{B4BCD9DD-A614-486E-B168-4D2B4820F2B4}"
	RootNamespace="EraserUI"
	Keyword="Win32Proj"
	TargetFrameworkVersion="131072"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
		<Platform
			Name="x64"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="4"
			InheritedPropertySheets="..\Deployment.vsprops"
			UseOfMFC="2"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="&quot;$(SOLUTIONDIR)&quot;"
				MinimalRebuild="true"
				ExceptionHandling="2"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="2"
				DebugInformationFormat="4"
				DisableSpecificWarnings="4244"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				AdditionalIncludeDirectories="$(SOLUTIONDIR)"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug|x64"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="4"
			InheritedPropertySheets="..\Deployment.vsprops"
			UseOfMFC="2"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="&quot;$(SOLUTIONDIR)&quot;"
				MinimalRebuild="true"
				ExceptionHandling="2"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="2"
				DebugInformationFormat="3"
				DisableSpecificWarnings="4244"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				AdditionalIncludeDirectories="$(SOLUTIONDIR)"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="4"
			InheritedPropertySheets="..\Deployment.vsprops"
			UseOfMFC="2"
			CharacterSet="2"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				AdditionalIncludeDirectories="&quot;$(SOLUTIONDIR)&quot;"
				PreprocessorDefinitions="NDEBUG"
				StringPooling="true"
				ExceptionHandling="2"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				DebugInformationFormat="3"
				DisableSpecificWarnings="4244"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				AdditionalIncludeDirectories="$(SOLUTIONDIR)"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|x64"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="4"
			InheritedPropertySheets="..\Deployment.vsprops"
			UseOfMFC="2"
			CharacterSet="2"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				AdditionalIncludeDirectories="&quot;$(SOLUTIONDIR)&quot;"
				PreprocessorDefinitions="NDEBUG"
				StringPooling="true"
				ExceptionHandling="2"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				DebugInformationFormat="3"
				DisableSpecificWarnings="4244;4267"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				AdditionalIncludeDirectories="$(SOLUTIONDIR)"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Standalone Release|Win32"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="4"
			InheritedPropertySheets="..\Deployment.vsprops"
			UseOfMFC="1"
			CharacterSet="2"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				AdditionalIncludeDirectories="&quot;$(SOLUTIONDIR)&quot;"
				PreprocessorDefinitions="NDEBUG;ERASER_STANDALONE"
				StringPooling="true"
				ExceptionHandling="2"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				DebugInformationFormat="3"
				DisableSpecificWarnings="4244"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				AdditionalIncludeDirectories="$(SOLUTIONDIR)"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Standalone Release|x64"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="4"
			InheritedPropertySheets="..\Deployment.vsprops"
			UseOfMFC="2"
			CharacterSet="2"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				AdditionalIncludeDirectories="&quot;$(SOLUTIONDIR)&quot;"
				PreprocessorDefinitions="NDEBUG;ERASER_STANDALONE"
				StringPooling="true"
				ExceptionHandling="2"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				DebugInformationFormat="3"
				DisableSpecificWarnings="4244"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				AdditionalIncludeDirectories="$(SOLUTIONDIR)"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release_Unicode|Win32"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="4"
			InheritedPropertySheets="..\Deployment.vsprops"
			UseOfMFC="2"
			CharacterSet="1"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				AdditionalIncludeDirectories="&quot;$(SOLUTIONDIR)&quot;"
				PreprocessorDefinitions="NDEBUG"
				StringPooling="true"
				ExceptionHandling="2"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				DebugInformationFormat="3"
				DisableSpecificWarnings="4244"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				AdditionalIncludeDirectories="$(SOLUTIONDIR)"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release_Unicode|x64"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="4"
			InheritedPropertySheets="..\Deployment.vsprops"
			UseOfMFC="2"
			CharacterSet="1"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				AdditionalIncludeDirectories="&quot;$(SOLUTIONDIR)&quot;"
				PreprocessorDefinitions="NDEBUG"
				StringPooling="true"
				ExceptionHandling="2"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				DebugInformationFormat="3"
				DisableSpecificWarnings="4244;4267"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				AdditionalIncludeDirectories="$(SOLUTIONDIR)"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug_Unicode|Win32"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="4"
			InheritedPropertySheets="..\Deployment.vsprops"
			UseOfMFC="2"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="&quot;$(SOLUTIONDIR)&quot;"
				MinimalRebuild="true"
				ExceptionHandling="2"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="2"
				DebugInformationFormat="4"
				DisableSpecificWarnings="4244"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				AdditionalIncludeDirectories="$(SOLUTIONDIR)"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug_Unicode|x64"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="4"
			InheritedPropertySheets="..\Deployment.vsprops"
			UseOfMFC="2"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="&quot;$(SOLUTIONDIR)&quot;"
				MinimalRebuild="true"
				ExceptionHandling="2"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="2"
				DebugInformationFormat="3"
				DisableSpecificWarnings="4244"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				AdditionalIncludeDirectories="$(SOLUTIONDIR)"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Standalone Release Unicode|Win32"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="4"
			InheritedPropertySheets="..\Deployment.vsprops"
			UseOfMFC="1"
			CharacterSet="1"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				AdditionalIncludeDirectories="&quot;$(SOLUTIONDIR)&quot;"
				PreprocessorDefinitions="NDEBUG;ERASER_STANDALONE"
				StringPooling="true"
				ExceptionHandling="2"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				DebugInformationFormat="3"
				DisableSpecificWarnings="4244"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				AdditionalIncludeDirectories="$(SOLUTIONDIR)"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Standalone Release Unicode|x64"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="4"
			InheritedPropertySheets="..\Deployment.vsprops"
			UseOfMFC="2"
			CharacterSet="1"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				AdditionalIncludeDirectories="&quot;$(SOLUTIONDIR)&quot;"
				PreprocessorDefinitions="NDEBUG;ERASER_STANDALONE"
				StringPooling="true"
				ExceptionHandling="2"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				DebugInformationFormat="3"
				DisableSpecificWarnings="4244"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				AdditionalIncludeDirectories="$(SOLUTIONDIR)"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx"
			UniqueIdentifier="{4FC737F1-C7A5-4376-A066-2A32D752A2FF}"
			>
			<File
				RelativePath="AlphaImageList.cpp"
				>
			</File>
			<File
				RelativePath="AlphaToolBar.cpp"
				>
			</File>
			<File
				RelativePath="DirDialog.cpp"
				>
			</File>
			<File
				RelativePath="DriveCombo.cpp"
				>
			</File>
			<File
				RelativePath="FileDialogEx.cpp"
				>
			</File>
			<File
				RelativePath="FileTreeCtrl.cpp"
				>
			</File>
			<File
				RelativePath="FitFileNameToScrn.cpp"
				>
			</File>
			<File
				RelativePath="FlatHeaderCtrl.cpp"
				>
			</File>
			<File
				RelativePath="FlatListCtrl.cpp"
				>
			</File>
			<File
				RelativePath="FlatListView.cpp"
				>
			</File>
			<File
				RelativePath="GfxGroupEdit.cpp"
				>
			</File>
			<File
				RelativePath="GfxOutBarCtrl.cpp"
				>
			</File>
			<File
				RelativePath="GfxPopupMenu.cpp"
				>
			</File>
			<File
				RelativePath="GfxSplitterWnd.cpp"
				>
			</File>
			<File
				RelativePath="HyperLink.cpp"
				>
			</File>
			<File
				RelativePath="InfoBar.cpp"
				>
			</File>
			<File
				RelativePath="InPlaceEdit.cpp"
				>
			</File>
			<File
				RelativePath="Masked.cpp"
				>
			</File>
			<File
				RelativePath="NewDialog.cpp"
				>
			</File>
			<File
				RelativePath="ProgressBar.cpp"
				>
			</File>
			<File
				RelativePath="ShellPidl.cpp"
				>
			</File>
			<File
				RelativePath="stdafx.cpp"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Standalone Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Standalone Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release_Unicode|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release_Unicode|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug_Unicode|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug_Unicode|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Standalone Release Unicode|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Standalone Release Unicode|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="TimeOutMessageBox.cpp"
				>
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl;inc;xsd"
			UniqueIdentifier="{93995380-89BD-4b04-88EB-625FBE52EBFB}"
			>
			<File
				RelativePath="AlphaImageList.h"
				>
			</File>
			<File
				RelativePath="AlphaToolBar.h"
				>
			</File>
			<File
				RelativePath="DirDialog.h"
				>
			</File>
			<File
				RelativePath="DriveCombo.h"
				>
			</File>
			<File
				RelativePath="FileDialogEx.h"
				>
			</File>
			<File
				RelativePath="FileTreeCtrl.h"
				>
			</File>
			<File
				RelativePath="FitFileNameToScrn.h"
				>
			</File>
			<File
				RelativePath="FlatHeaderCtrl.h"
				>
			</File>
			<File
				RelativePath="FlatListCtrl.h"
				>
			</File>
			<File
				RelativePath="FlatListView.h"
				>
			</File>
			<File
				RelativePath="GfxGroupEdit.h"
				>
			</File>
			<File
				RelativePath="GfxOutBarCtrl.h"
				>
			</File>
			<File
				RelativePath="GfxPopupMenu.h"
				>
			</File>
			<File
				RelativePath="GfxSplitterWnd.h"
				>
			</File>
			<File
				RelativePath="HyperLink.h"
				>
			</File>
			<File
				RelativePath="InfoBar.h"
				>
			</File>
			<File
				RelativePath="InPlaceEdit.h"
				>
			</File>
			<File
				RelativePath="Masked.h"
				>
			</File>
			<File
				RelativePath="MemDC.h"
				>
			</File>
			<File
				RelativePath="NewDialog.h"
				>
			</File>
			<File
				RelativePath="ProgressBar.h"
				>
			</File>
			<File
				RelativePath=".\resource.h"
				>
			</File>
			<File
				RelativePath="ShellPidl.h"
				>
			</File>
			<File
				RelativePath="stdafx.h"
				>
			</File>
			<File
				RelativePath="TimeOutMessageBox.h"
				>
			</File>
			<File
				RelativePath="VisualStyles.h"
				>
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav"
			UniqueIdentifier="{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}"
			>
			<File
				RelativePath=".\EraserUI.rc"
				>
			</File>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
