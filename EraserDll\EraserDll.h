﻿// EraserDll.h - Eraser 动态链接库主头文件
//
// 本文件定义了 Eraser 安全删除库的所有公共接口、数据类型、常量和函数声明
// 这是使用 Eraser 库进行安全文件删除功能的主要接口文件
//
// 主要功能：
// - 定义库的基本数据类型和常量
// - 声明所有导出的API函数
// - 定义错误码和状态值
// - 提供进度通知和统计信息接口

#ifndef ERASERDLL_H
#define ERASERDLL_H

#include "EraserExport.h"

// ============================================================================
// 全局唯一标识符 (GUID)
// ============================================================================
#ifndef _GUID_ERASER
    // Eraser 程序的唯一标识符，用于COM组件注册和识别
    #define _GUID_ERASER "Eraser.{D5BBB6C1-64F1-11d1-A87C-************}"
#endif


// ============================================================================
// 注册表键值定义 (存储在 HKEY_CURRENT_USER 下)
// ============================================================================

// 基础注册表路径
const LPCTSTR ERASER_REGISTRY_AUTHOR
    = _T("Software\\Heidi Computers Ltd");                    // 软件作者路径
const LPCTSTR ERASER_REGISTRY_PROGRAM
    = _T("Software\\Heidi Computers Ltd\\Eraser");           // 程序主路径
const LPCTSTR ERASER_REGISTRY_BASE
    = _T("Software\\Heidi Computers Ltd\\Eraser\\5.8");      // 版本特定路径

// 库设置相关键值
const LPCTSTR ERASER_REGISTRY_LIBRARY
    = _T("Library");                                          // 库设置
const LPCTSTR ERASER_REGISTRY_LIBRARY_VERSION
    = _T("LibraryVersion");                                   // 库版本信息

// Shell 扩展相关设置
const LPCTSTR ERASEXT_REGISTRY_ENABLED
    = _T("ErasextEnabled");                                   // 是否启用Shell扩展
const LPCTSTR ERASEXT_REGISTRY_RESULTS
    = _T("ResultsErasext");                                   // Shell扩展结果设置

// 随机数生成器设置
const LPCTSTR ERASER_RANDOM_SLOW_POLL
    = _T("EraserSlowPollEnabled");                           // 是否启用慢速熵轮询

// 结果显示设置
const LPCTSTR ERASER_REGISTRY_RESULTS_WHENFAILED
    = _T("ResultsOnlyWhenFailed");                           // 仅在失败时显示结果
const LPCTSTR ERASER_REGISTRY_RESULTS_FILES
    = _T("ResultsForFiles");                                 // 文件操作结果设置
const LPCTSTR ERASER_REGISTRY_RESULTS_UNUSEDSPACE
    = _T("ResultsForUnusedSpace");                           // 未使用空间擦除结果设置


// ============================================================================
// 网址链接定义
// ============================================================================
const LPCTSTR ERASER_URL_HOMEPAGE
    = _T("http://www.heidi.ie/eraser/");                     // 官方主页地址
const LPCTSTR ERASER_URL_EMAIL
    = _T("mailto:<EMAIL>");                         // 技术支持邮箱

// 编译器兼容性处理
#ifdef DMARS
typedef unsigned __int64 ULONGLONG;
#endif

// ============================================================================
// 库基础数据类型定义
// ============================================================================

// 有符号整数类型及其指针类型
typedef char        E_INT8,   *E_PINT8;                     // 8位有符号整数
typedef short       E_INT16,  *E_PINT16;                    // 16位有符号整数
typedef LONG        E_INT32,  *E_PINT32;                    // 32位有符号整数
typedef LONGLONG    E_INT64,  *E_PINT64;                    // 64位有符号整数

// 无符号整数类型及其指针类型
typedef BYTE        E_UINT8,  *E_PUINT8;                    // 8位无符号整数
typedef WORD        E_UINT16, *E_PUINT16;                   // 16位无符号整数
typedef ULONG       E_UINT32, *E_PUINT32;                   // 32位无符号整数
typedef ULONGLONG   E_UINT64, *E_PUINT64;                   // 64位无符号整数

// 参数方向修饰符（用于文档和代码可读性）
#define E_IN        const                                    // 输入参数
#define E_OUT                                                // 输出参数
#define E_INOUT                                              // 输入输出参数


// ============================================================================
// 窗口消息定义
// ============================================================================
#define WM_ERASERNOTIFY     (WM_USER + 10)                   // Eraser通知消息

// wParam 参数值定义（用于WM_ERASERNOTIFY消息）
#define ERASER_WIPE_BEGIN   0                                // 擦除开始
#define ERASER_WIPE_UPDATE  1                                // 擦除进度更新
#define ERASER_WIPE_DONE    2                                // 擦除完成
#define ERASER_TEST_PAUSED  3                                // 测试模式暂停

// ============================================================================
// 库类型定义
// ============================================================================

// Eraser 操作句柄类型（用于标识一个擦除操作上下文）
typedef E_UINT32 ERASER_HANDLE;

// 擦除方法枚举
typedef enum {
    ERASER_METHOD_LIBRARY,          // 库默认方法
    ERASER_METHOD_GUTMANN,          // Gutmann 35遍擦除方法
    ERASER_METHOD_DOD,              // 美国国防部标准方法
    ERASER_METHOD_DOD_E,            // 美国国防部扩展方法
    ERASER_METHOD_PSEUDORANDOM,     // 伪随机数据擦除
	ERASER_METHOD_FIRST_LAST_2KB,   // 仅擦除文件首末2KB
    ERASER_METHOD_SCHNEIER          // Schneier 7遍擦除方法
} ERASER_METHOD;

// 验证擦除方法是否有效的宏
#define eraserIsValidMethod(x)  (( (x) >= ERASER_METHOD_LIBRARY ) && \
                                 ( (x) <= ERASER_METHOD_SCHNEIER ))

// 数据类型枚举（指定要擦除的数据类型）
typedef enum {
    ERASER_DATA_DRIVES,             // 驱动器（未使用空间擦除）
    ERASER_DATA_FILES               // 文件（文件内容擦除）
} ERASER_DATA_TYPE;

// 验证数据类型是否有效的宏
#define eraserIsValidDataType(x)  (( (x) >= ERASER_DATA_DRIVES ) && \
                                   ( (x) <= ERASER_DATA_FILES ))

// 选项页面枚举（用于显示不同的设置页面）
typedef enum {
    ERASER_PAGE_DRIVE,              // 驱动器选项页面
    ERASER_PAGE_FILES               // 文件选项页面
} ERASER_OPTIONS_PAGE;

// eraserRemoveFolder 函数的选项标志
enum {
    ERASER_REMOVE_FOLDERONLY  = 0,  // 仅删除文件夹本身
    ERASER_REMOVE_RECURSIVELY = 1   // 递归删除文件夹及其内容
};

// ============================================================================
// 显示标志定义（控制UI显示哪些信息）
// ============================================================================
enum {
    eraserDispPass     = (1 << 0),     // 显示擦除遍数信息
    eraserDispTime     = (1 << 1),     // 显示预计剩余时间
    eraserDispMessage  = (1 << 2),     // [未使用] 显示消息
    eraserDispProgress = (1 << 3),     // [未使用] 显示进度条
    eraserDispStop     = (1 << 4),     // [未使用] 允许终止操作
    eraserDispItem     = (1 << 5),     // [未使用] 显示项目名称
    eraserDispInit     = (1 << 6),     // 在ERASER_WIPE_BEGIN时将进度设为0
    eraserDispReserved = (1 << 7)      // [未使用] 保留标志
};

// ============================================================================
// 擦除项目位掩码定义（指定要擦除的具体内容）
// ============================================================================
enum {
    // 文件相关擦除项目
    fileClusterTips      = (1 << 0),   // 文件簇尾部（slack space）
    fileNames            = (1 << 1),   // 文件名
    fileAlternateStreams = (1 << 2),   // 备用数据流（NTFS ADS）

    // 磁盘未使用空间相关擦除项目
    diskFreeSpace        = (1 << 5),   // 磁盘剩余空间
    diskClusterTips      = (1 << 6),   // 磁盘簇尾部
    diskDirEntries       = (1 << 7)    // 目录项（已删除文件的目录信息）
};


// ============================================================================
// 错误码定义
// ============================================================================

// Eraser 操作结果类型
typedef E_INT32 ERASER_RESULT;

// 成功和通用错误码
#define ERASER_OK                       0       // 操作成功，无错误
#define ERASER_ERROR                    -1      // 未指定的通用错误

// 参数错误码（-2 到 -7）
#define ERASER_ERROR_PARAM1             -2      // 参数1无效
#define ERASER_ERROR_PARAM2             -3      // 参数2无效
#define ERASER_ERROR_PARAM3             -4      // 参数3无效
#define ERASER_ERROR_PARAM4             -5      // 参数4无效
#define ERASER_ERROR_PARAM5             -6      // 参数5无效
#define ERASER_ERROR_PARAM6             -7      // 参数6无效

// 系统资源错误码
#define ERASER_ERROR_MEMORY             -8      // 内存不足
#define ERASER_ERROR_THREAD             -9      // 线程启动失败

// 程序逻辑错误码
#define ERASER_ERROR_EXCEPTION          -10     // 异常错误（程序BUG）
#define ERASER_ERROR_CONTEXT            -11     // 上下文数组已满（达到ERASER_MAX_CONTEXT限制）
#define ERASER_ERROR_INIT               -12     // 库未初始化（需要先调用eraserInit()）

// 状态相关错误码
#define ERASER_ERROR_RUNNING            -13     // 操作失败，因为线程正在运行
#define ERASER_ERROR_NOTRUNNING         -14     // 操作失败，因为线程未运行
#define ERASER_ERROR_DENIED             -15     // 操作被拒绝（权限不足）

// 功能实现错误码
#define ERASER_ERROR_NOTIMPLEMENTED     -32     // 功能未实现

// 错误检查宏
#define eraserOK(x)     ((x) >= ERASER_OK)      // 检查操作是否成功
#define eraserError(x)  (!eraserOK(x))          // 检查操作是否失败

// 无效上下文标识（仅用于标记上下文无效，验证句柄请使用eraserIsValidContext）
#define ERASER_INVALID_CONTEXT          ((ERASER_HANDLE)-1)


// ============================================================================
// 导出函数定义
// ============================================================================

#if defined(__cplusplus)
extern "C" {
#endif


// ============================================================================
// 库初始化和清理函数
// ============================================================================

/**
 * 初始化Eraser库
 * @return ERASER_RESULT 操作结果
 * @note 在使用库的任何其他功能之前必须先调用此函数
 */
ERASER_EXPORT
eraserInit();

/**
 * 清理Eraser库资源
 * @return ERASER_RESULT 操作结果
 * @note 程序结束前应调用此函数释放库占用的资源
 */
ERASER_EXPORT
eraserEnd();


// ============================================================================
// 上下文创建和销毁函数
// ============================================================================

/**
 * 将ERASER_METHOD枚举转换为内部格式
 * @param mIn 擦除方法枚举值
 * @return E_UINT8 内部格式的方法标识
 */
ERASER_API E_UINT8 convEraseMethod(ERASER_METHOD mIn);

/**
 * 使用预定义设置创建擦除上下文
 * @param handle 输出参数，返回创建的上下文句柄
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserCreateContext(E_OUT ERASER_HANDLE* handle);

/**
 * 创建擦除上下文并设置自定义参数
 * @param handle 输出参数，返回创建的上下文句柄
 * @param method 擦除方法
 * @param passes 擦除遍数
 * @param items 要擦除的项目标志
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserCreateContextEx(E_OUT ERASER_HANDLE* handle, E_IN E_UINT8 method, E_IN E_UINT16 passes, E_IN E_UINT8 items);

/**
 * 销毁擦除上下文
 * @param handle 要销毁的上下文句柄
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserDestroyContext(E_IN ERASER_HANDLE handle);

/**
 * 检查上下文句柄的有效性
 * @param handle 要检查的上下文句柄
 * @return ERASER_RESULT ERASER_OK表示有效，其他值表示无效
 */
ERASER_EXPORT
eraserIsValidContext(E_IN ERASER_HANDLE handle);


// ============================================================================
// 错误处理函数
// ============================================================================

/**
 * 错误处理回调函数类型定义
 * @param filename 发生错误的文件名
 * @param dwErrorCode 错误代码
 * @param ctx 上下文指针
 * @param param 用户自定义参数
 * @return DWORD 处理结果
 */
typedef DWORD (*EraserErrorHandler) (LPCTSTR /*filename*/, DWORD /*dwErrorCode*/, void* /*ctx*/, void* /*param*/);

/**
 * 设置错误处理回调函数
 * @param handle 上下文句柄
 * @param pfn 错误处理回调函数指针
 * @param fnParam 传递给回调函数的用户参数
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserSetErrorHandler(E_IN ERASER_HANDLE handle, EraserErrorHandler pfn, void* fnParam);

// ============================================================================
// 数据类型管理函数
// ============================================================================

/**
 * 设置上下文的数据类型
 * @param handle 上下文句柄
 * @param dataType 数据类型（ERASER_DATA_DRIVES 或 ERASER_DATA_FILES）
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserSetDataType(E_IN ERASER_HANDLE handle, E_IN ERASER_DATA_TYPE dataType);

/**
 * 获取上下文的数据类型
 * @param handle 上下文句柄
 * @param dataType 输出参数，返回数据类型
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserGetDataType(E_IN ERASER_HANDLE handle, E_OUT ERASER_DATA_TYPE* dataType);


// ============================================================================
// 数据项管理函数
// ============================================================================

/**
 * 向上下文数据数组添加项目
 * @param handle 上下文句柄
 * @param data 要添加的数据项（文件路径或驱动器路径）
 * @param length 数据长度
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserAddItem(E_IN ERASER_HANDLE handle, E_IN LPVOID data, E_IN E_UINT16 length);

/**
 * 设置完成后的操作
 * @param handle 上下文句柄
 * @param action ExitWindowsEx函数的标志（如关机、重启等）
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserSetFinishAction(E_IN ERASER_HANDLE handle, E_IN DWORD action);

/**
 * 清空上下文数据数组
 * @param handle 上下文句柄
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserClearItems(E_IN ERASER_HANDLE handle);


// ============================================================================
// 通知机制函数
// ============================================================================

/**
 * 设置接收通知的窗口句柄
 * @param handle 上下文句柄
 * @param hwnd 要接收WM_ERASERNOTIFY消息的窗口句柄
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserSetWindow(E_IN ERASER_HANDLE handle, E_IN HWND hwnd);

/**
 * 获取接收通知的窗口句柄
 * @param handle 上下文句柄
 * @param hwnd 输出参数，返回窗口句柄
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserGetWindow(E_IN ERASER_HANDLE handle, E_OUT HWND* hwnd);

/**
 * 设置窗口消息ID
 * @param handle 上下文句柄
 * @param message 自定义消息ID（默认为WM_ERASERNOTIFY）
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserSetWindowMessage(E_IN ERASER_HANDLE handle, E_IN E_UINT32 message);

/**
 * 获取窗口消息ID
 * @param handle 上下文句柄
 * @param message 输出参数，返回消息ID
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserGetWindowMessage(E_IN ERASER_HANDLE handle, E_OUT E_PUINT32 message);


// ============================================================================
// 统计信息函数
// ============================================================================

/**
 * 获取已擦除的区域大小
 * @param handle 上下文句柄
 * @param area 输出参数，返回擦除区域大小（字节）
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserStatGetArea(E_IN ERASER_HANDLE handle, E_OUT E_PUINT64 area);

/**
 * 获取已擦除的簇尾部区域大小
 * @param handle 上下文句柄
 * @param tips 输出参数，返回簇尾部擦除大小（字节）
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserStatGetTips(E_IN ERASER_HANDLE handle, E_OUT E_PUINT64 tips);

/**
 * 获取实际写入的数据量
 * @param handle 上下文句柄
 * @param wiped 输出参数，返回写入数据量（字节）
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserStatGetWiped(E_IN ERASER_HANDLE handle, E_OUT E_PUINT64 wiped);

/**
 * 获取操作耗时
 * @param handle 上下文句柄
 * @param time 输出参数，返回耗时（毫秒）
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserStatGetTime(E_IN ERASER_HANDLE handle, E_OUT E_PUINT32 time);


// ============================================================================
// 显示控制函数
// ============================================================================

/**
 * 获取UI应该显示的内容标志
 * @param handle 上下文句柄
 * @param flags 输出参数，返回显示标志（参见eraserDisp*常量定义）
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserDispFlags(E_IN ERASER_HANDLE handle, E_OUT E_PUINT8 flags);


// ============================================================================
// 进度信息函数
// ============================================================================

/**
 * 获取操作完成的预计剩余时间
 * @param handle 上下文句柄
 * @param timeLeft 输出参数，返回预计剩余时间（秒）
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserProgGetTimeLeft(E_IN ERASER_HANDLE handle, E_OUT E_PUINT32 timeLeft);

/**
 * 获取当前项目的完成百分比
 * @param handle 上下文句柄
 * @param percent 输出参数，返回完成百分比（0-100）
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
********************(E_IN ERASER_HANDLE handle, E_OUT E_PUINT8 percent);

/**
 * 获取整个操作的完成百分比
 * @param handle 上下文句柄
 * @param totalPercent 输出参数，返回总体完成百分比（0-100）
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserProgGetTotalPercent(E_IN ERASER_HANDLE handle, E_OUT E_PUINT8 totalPercent);

/**
 * 获取当前擦除遍数的索引
 * @param handle 上下文句柄
 * @param currentPass 输出参数，返回当前遍数索引（从1开始）
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserProgGetCurrentPass(E_IN ERASER_HANDLE handle, E_OUT E_PUINT16 currentPass);

/**
 * 获取总擦除遍数
 * @param handle 上下文句柄
 * @param passes 输出参数，返回总遍数
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserProgGetPasses(E_IN ERASER_HANDLE handle, E_OUT E_PUINT16 passes);

/**
 * 获取可显示给用户的状态消息
 * @param handle 上下文句柄
 * @param message 输出缓冲区，存储消息文本
 * @param length 输入输出参数，输入时为缓冲区大小，输出时为实际消息长度
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserProgGetMessage(E_IN ERASER_HANDLE handle, E_OUT LPVOID message, E_INOUT E_PUINT16 length);

/**
 * 获取当前正在处理的项目名称
 * @param handle 上下文句柄
 * @param dataString 输出缓冲区，存储项目名称
 * @param length 输入输出参数，输入时为缓冲区大小，输出时为实际名称长度
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserProgGetCurrentDataString(E_IN ERASER_HANDLE handle, E_OUT LPVOID dataString, E_INOUT E_PUINT16 length);



// ============================================================================
// 操作控制函数
// ============================================================================

/**
 * 在新线程中开始擦除操作（异步）
 * @param handle 上下文句柄
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserStart(E_IN ERASER_HANDLE handle);

/**
 * 开始擦除操作（同步）
 * @param handle 上下文句柄
 * @return ERASER_RESULT 操作结果
 * @note 此函数会阻塞直到操作完成
 */
ERASER_EXPORT
eraserStartSync(E_IN ERASER_HANDLE handle);

/**
 * 停止正在运行的擦除任务
 * @param handle 上下文句柄
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserStop(E_IN ERASER_HANDLE handle);

/**
 * 检查任务是否正在运行
 * @param handle 上下文句柄
 * @param running 输出参数，返回运行状态（非0表示正在运行）
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserIsRunning(E_IN ERASER_HANDLE handle, E_OUT E_PUINT8 running);


// ============================================================================
// 结果查询函数
// ============================================================================

/**
 * 检查任务是否成功完成
 * @param handle 上下文句柄
 * @param completed 输出参数，返回完成状态（非0表示已完成）
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserCompleted(E_IN ERASER_HANDLE handle, E_OUT E_PUINT8 completed);

/**
 * 检查任务是否失败
 * @param handle 上下文句柄
 * @param failed 输出参数，返回失败状态（非0表示失败）
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserFailed(E_IN ERASER_HANDLE handle, E_OUT E_PUINT8 failed);

/**
 * 检查任务是否被终止
 * @param handle 上下文句柄
 * @param terminated 输出参数，返回终止状态（非0表示被终止）
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserTerminated(E_IN ERASER_HANDLE handle, E_OUT E_PUINT8 terminated);

/**
 * 获取错误消息的数量
 * @param handle 上下文句柄
 * @param count 输出参数，返回错误消息数量
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserErrorStringCount(E_IN ERASER_HANDLE handle, E_OUT E_PUINT16 count);

/**
 * 获取指定索引的错误消息
 * @param handle 上下文句柄
 * @param index 错误消息索引（从0开始）
 * @param errorString 输出缓冲区，存储错误消息
 * @param length 输入输出参数，输入时为缓冲区大小，输出时为实际消息长度
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserErrorString(E_IN ERASER_HANDLE handle, E_IN E_UINT16 index, E_OUT LPVOID errorString, E_INOUT E_PUINT16 length);

/**
 * 获取失败项目的数量
 * @param handle 上下文句柄
 * @param count 输出参数，返回失败项目数量
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserFailedCount(E_IN ERASER_HANDLE handle, E_OUT E_PUINT32 count);

/**
 * 获取指定索引的失败项目名称
 * @param handle 上下文句柄
 * @param index 失败项目索引（从0开始）
 * @param failedString 输出缓冲区，存储失败项目名称
 * @param length 输入输出参数，输入时为缓冲区大小，输出时为实际名称长度
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserFailedString(E_IN ERASER_HANDLE handle, E_IN E_UINT32 index, E_OUT LPVOID failedString, E_INOUT E_PUINT16 length);


// ============================================================================
// 报告显示函数
// ============================================================================

/**
 * 显示擦除操作报告对话框
 * @param handle 上下文句柄
 * @param hwndParent 父窗口句柄
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserShowReport(E_IN ERASER_HANDLE handle, E_IN HWND hwndParent);


// ============================================================================
// 选项设置函数
// ============================================================================

/**
 * 显示库选项设置窗口
 * @param hwndParent 父窗口句柄
 * @param page 要显示的选项页面
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserShowOptions(E_IN HWND hwndParent, E_IN ERASER_OPTIONS_PAGE page);


// ============================================================================
// 文件/目录删除函数
// ============================================================================

/**
 * 安全删除文件
 * @param filePath 文件路径
 * @param length 路径长度
 * @return ERASER_RESULT 操作结果
 * @note 此函数会安全地删除文件名，防止文件名恢复
 */
ERASER_EXPORT
eraserRemoveFile(E_IN LPVOID filePath, E_IN E_UINT16 length);

/**
 * 删除文件夹
 * @param folderPath 文件夹路径
 * @param length 路径长度
 * @param options 删除选项（ERASER_REMOVE_FOLDERONLY 或 ERASER_REMOVE_RECURSIVELY）
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserRemoveFolder(E_IN LPVOID folderPath, E_IN E_UINT16 length, E_IN E_UINT8 options);


// ============================================================================
// 辅助工具函数
// ============================================================================

/**
 * 获取驱动器的剩余磁盘空间
 * @param drivePath 驱动器路径（如"C:\\"）
 * @param length 路径长度
 * @param freeSpace 输出参数，返回剩余空间大小（字节）
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserGetFreeDiskSpace(E_IN LPVOID drivePath, E_IN E_UINT16 length, E_OUT E_PUINT64 freeSpace);

/**
 * 获取分区的簇大小
 * @param drivePath 驱动器路径（如"C:\\"）
 * @param length 路径长度
 * @param clusterSize 输出参数，返回簇大小（字节）
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserGetClusterSize(E_IN LPVOID drivePath, E_IN E_UINT16 length, E_OUT E_PUINT32 clusterSize);


// ============================================================================
// 测试模式函数
// ============================================================================

/**
 * 启用测试模式
 * @param handle 上下文句柄
 * @return ERASER_RESULT 操作结果
 * @note 测试模式下文件将以共享方式打开，每次擦除遍数后会暂停，
 *       直到调用eraserTestContinueProcess()继续
 */
ERASER_EXPORT
eraserTestEnable(E_IN ERASER_HANDLE handle);

/**
 * 继续测试模式下暂停的擦除过程
 * @param handle 上下文句柄
 * @return ERASER_RESULT 操作结果
 */
ERASER_EXPORT
eraserTestContinueProcess(E_IN ERASER_HANDLE handle);


#if defined(__cplusplus)
}
#endif


// ============================================================================
// 辅助宏定义
// ============================================================================

// 调试模式下的默认分支处理
#ifdef DEBUG
    #define NODEFAULT ASSERT(0)        // 调试模式：触发断言
#else
    #define NODEFAULT __assume(0)      // 发布模式：编译器优化提示
#endif

// 文件夹名称检查宏（检查是否为"."或".."）
#define ISNT_SUBFOLDER(lpsz) \
    ((lpsz)[0] == _T('.') && \
     ((lpsz)[1] == _T('\0') || \
      ((lpsz)[1] == _T('.') && \
       (lpsz)[2] == _T('\0'))))
#define IS_SUBFOLDER(lpsz) \
    (!ISNT_SUBFOLDER(lpsz))

// ============================================================================
// 位掩码操作宏
// ============================================================================

/**
 * 检查指定位是否被设置
 * @param x 要检查的值
 * @param mask 位掩码
 * @return 非0表示位被设置，0表示位未设置
 */
#define bitSet(x, mask) \
    (((x) & (mask)) != 0)

/**
 * 设置指定位
 * @param x 要修改的值
 * @param mask 位掩码
 */
#define setBit(x, mask) \
    (x) |= (mask)

/**
 * 清除指定位
 * @param x 要修改的值
 * @param mask 位掩码
 */
#define unsetBit(x, mask) \
    (x) &= ~(mask)

// ============================================================================
// 库内部头文件包含
// ============================================================================
#ifdef _DLL_ERASER
    #include "EraserDllInternal.h"     // 库内部使用的头文件
#endif

#endif // ERASERDLL_H

// ============================================================================
// 文件结束
//
// 本头文件定义了Eraser安全删除库的完整公共接口，包括：
// - 基础数据类型和常量定义
// - 错误码和状态值定义
// - 所有导出函数的声明和参数说明
// - 进度通知和统计信息接口
// - 辅助工具函数和宏定义
//
// 使用此库时，只需包含此头文件即可访问所有功能。
// ============================================================================