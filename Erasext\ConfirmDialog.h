﻿// ConfirmDialog.h

#ifndef CONFIRMDIALOG_H
#define CONFIRMDIALOG_H

/////////////////////////////////////////////////////////////////////////////
// CConfirmDialog dialog

class CConfirmDialog : public CDialog
{
// Construction
public:
    BOOL    m_bSingleFile;
    BOOL    m_bUseFiles;
    BOOL    m_bMove;
    CString m_strData;
    CString m_strTarget;

    CConfirmDialog(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
    //{{AFX_DATA(CConfirmDialog)
    enum { IDD = IDD_DIALOG_CONFIRM };
    CString m_strLineOne;
    CString m_strLineTwo;
    //}}AFX_DATA


// Overrides
    // ClassWizard generated virtual function overrides
    //{{AFX_VIRTUAL(CConfirmDialog)
    public:
    virtual BOOL PreTranslateMessage(MSG* pMsg);
    protected:
    virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
    //}}AFX_VIRTUAL

// Implementation
protected:
    HACCEL m_hAccel;

    // Generated message map functions
    //{{AFX_MSG(CConfirmDialog)
    virtual BOOL OnInitDialog();
    afx_msg void OnOptions();
    afx_msg void OnYes();
    virtual void OnCancel();
    //}}AFX_MSG
    DECLARE_MESSAGE_MAP()
};

#endif
