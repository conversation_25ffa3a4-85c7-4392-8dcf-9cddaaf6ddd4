﻿#if !defined(AFX_VERIFY_H__27133678_2120_4B3E_9B6B_3B9BDF651E07__INCLUDED_)
#define AFX_VERIFY_H__27133678_2120_4B3E_9B6B_3B9BDF651E07__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#ifndef __AFXWIN_H__
	#error include 'stdafx.h' before including this file for PCH
#endif

#include "resource.h"		// main symbols

/////////////////////////////////////////////////////////////////////////////
// CVerifyApp:
// See Verify.cpp for the implementation of this class
//

class CVerifyApp : public CWinApp
{
public:
	CVerifyApp();

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CVerifyApp)
	public:
	virtual BOOL InitInstance();
	virtual int ExitInstance();
	//}}AFX_VIRTUAL

// Implementation

	//{{AFX_MSG(CVerifyApp)
		// NOTE - the ClassWizard will add and remove member functions here.
		//    DO NOT EDIT what you see in these blocks of generated code !
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};


/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_VERIFY_H__27133678_2120_4B3E_9B6B_3B9BDF651E07__INCLUDED_)
