﻿C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v150\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v150\Microsoft.CppBuild.targets(1397,5): warning MSB8012: TargetPath(C:\Users\<USER>\Desktop\Eraser\src\bin\Win32\Debug\EraserUI.lib) 与 Library 的 OutputFile 属性值(C:\Users\<USER>\Desktop\Eraser\src\Lib\OutD\EraserUI.lib)不匹配。这可能导致项目生成不正确。若要更正此问题，请确保 $(OutDir)、$(TargetName) 和 $(TargetExt) 属性值与 %(Lib.OutputFile) 中指定的值匹配。
LINK : warning LNK4068: 未指定 /MACHINE；默认设置为 X86
  EraserUI.vcxproj -> C:\Users\<USER>\Desktop\Eraser\src\bin\Win32\Debug\EraserUI.lib
