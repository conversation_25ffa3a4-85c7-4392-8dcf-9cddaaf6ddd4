﻿C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v150\Microsoft.Cpp.Common.props(209,5): warning MSB4211: 正在首次将属性“VC_ExecutablePath_x64_x64”设置为某个值，但已在“C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v170\Microsoft.Cpp.Analysis.props (23,5)”处使用了该属性。
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v150\Microsoft.CppBuild.targets(948,5): warning MSB4211: 正在首次将属性“GetTargetPathDependsOn”设置为某个值，但已在“C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\amd64\Microsoft.Common.CurrentVersion.targets (2243,5)”处使用了该属性。
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v150\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
EraserUI.lib(FitFileNameToScrn.obj) : warning LNK4075: 忽略“/EDITANDCONTINUE”(由于“/OPT:ICF”规范)
    正在创建库 C:\Users\<USER>\Desktop\Eraser\5.8.8\Erasext\bin\Win32\Release\Erasext.lib 和对象 C:\Users\<USER>\Desktop\Eraser\5.8.8\Erasext\bin\Win32\Release\Erasext.exp
LINK : warning LNK4098: 默认库“mfc140d.lib”与其他库的使用冲突；请使用 /NODEFAULTLIB:library
LINK : warning LNK4098: 默认库“mfcs140d.lib”与其他库的使用冲突；请使用 /NODEFAULTLIB:library
LINK : warning LNK4098: 默认库“msvcrtd.lib”与其他库的使用冲突；请使用 /NODEFAULTLIB:library
ConfirmDialog.obj : error LNK2001: 无法解析的外部符号 __imp__eraserShowOptions@8
Erasext.obj : error LNK2001: 无法解析的外部符号 __imp__eraserInit@0
Erasext.obj : error LNK2001: 无法解析的外部符号 __imp__eraserEnd@0
ErasextMenu.obj : error LNK2001: 无法解析的外部符号 __imp__eraserRemoveFolder@12
ErasextMenu.obj : error LNK2001: 无法解析的外部符号 "__declspec(dllimport) public: __thiscall CFileLockResolver::~CFileLockResolver(void)" (__imp_??1CFileLockResolver@@QAE@XZ)
WipeProgDlg.obj : error LNK2001: 无法解析的外部符号 "__declspec(dllimport) public: __thiscall CFileLockResolver::CFileLockResolver(int)" (__imp_??0CFileLockResolver@@QAE@H@Z)
WipeProgDlg.obj : error LNK2001: 无法解析的外部符号 __imp__eraserIsValidContext@4
WipeProgDlg.obj : error LNK2001: 无法解析的外部符号 __imp__eraserCreateContext@4
WipeProgDlg.obj : error LNK2001: 无法解析的外部符号 "__declspec(dllimport) public: void __thiscall CFileLockResolver::SetHandle(unsigned long)" (__imp_?SetHandle@CFileLockResolver@@QAEXK@Z)
WipeProgDlg.obj : error LNK2001: 无法解析的外部符号 __imp__eraserSetDataType@8
WipeProgDlg.obj : error LNK2001: 无法解析的外部符号 __imp__eraserAddItem@12
WipeProgDlg.obj : error LNK2001: 无法解析的外部符号 __imp__eraserSetWindow@8
WipeProgDlg.obj : error LNK2001: 无法解析的外部符号 __imp__eraserSetWindowMessage@8
WipeProgDlg.obj : error LNK2001: 无法解析的外部符号 __imp__eraserStart@4
WipeProgDlg.obj : error LNK2001: 无法解析的外部符号 __imp__eraserProgGetCurrentDataString@12
WipeProgDlg.obj : error LNK2001: 无法解析的外部符号 __imp__eraserProgGetMessage@12
WipeProgDlg.obj : error LNK2001: 无法解析的外部符号 __imp__eraserDispFlags@8
WipeProgDlg.obj : error LNK2001: 无法解析的外部符号 __imp__eraserProgGetPercent@8
WipeProgDlg.obj : error LNK2001: 无法解析的外部符号 __imp__eraserProgGetTotalPercent@8
WipeProgDlg.obj : error LNK2001: 无法解析的外部符号 __imp__eraserProgGetCurrentPass@8
WipeProgDlg.obj : error LNK2001: 无法解析的外部符号 __imp__eraserProgGetPasses@8
WipeProgDlg.obj : error LNK2001: 无法解析的外部符号 __imp__eraserProgGetTimeLeft@8
WipeProgDlg.obj : error LNK2001: 无法解析的外部符号 __imp__eraserFailedCount@8
WipeProgDlg.obj : error LNK2001: 无法解析的外部符号 __imp__eraserErrorStringCount@8
WipeProgDlg.obj : error LNK2001: 无法解析的外部符号 __imp__eraserShowReport@8
WipeProgDlg.obj : error LNK2001: 无法解析的外部符号 __imp__eraserCompleted@8
WipeProgDlg.obj : error LNK2001: 无法解析的外部符号 "__declspec(dllimport) public: void __thiscall CFileLockResolver::Close(void)" (__imp_?Close@CFileLockResolver@@QAEXXZ)
WipeProgDlg.obj : error LNK2001: 无法解析的外部符号 __imp__eraserDestroyContext@4
WipeProgDlg.obj : error LNK2001: 无法解析的外部符号 __imp__eraserStop@4
EraserUI.lib(FitFileNameToScrn.obj) : error LNK2001: 无法解析的外部符号 __imp___CrtDbgReport
Shared.lib(SeException.obj) : error LNK2001: 无法解析的外部符号 __imp___CrtDbgReport
Shared.lib(key.obj) : error LNK2001: 无法解析的外部符号 __imp___CrtDbgReport
Shared.lib(FileHelper.obj) : error LNK2001: 无法解析的外部符号 __imp___CrtDbgReport
Shared.lib(FileHelper.obj) : error LNK2001: 无法解析的外部符号 __imp___CrtDbgReportW
Shared.lib(stdafx.obj) : error LNK2001: 无法解析的外部符号 __imp___CrtDbgReportW
EraserUI.lib(FitFileNameToScrn.obj) : error LNK2001: 无法解析的外部符号 __imp___CrtDbgReportW
EraserUI.lib(stdafx.obj) : error LNK2001: 无法解析的外部符号 __imp___CrtDbgReportW
Shared.lib(SeException.obj) : error LNK2001: 无法解析的外部符号 __imp___CrtDbgReportW
Shared.lib(key.obj) : error LNK2001: 无法解析的外部符号 __imp___CrtDbgReportW
C:\Users\<USER>\Desktop\Eraser\5.8.8\Erasext\bin\Win32\Release\Erasext.dll : fatal error LNK1120: 31 个无法解析的外部命令
