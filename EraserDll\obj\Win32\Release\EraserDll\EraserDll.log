﻿C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v150\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  StdAfx.cpp
  ByteEdit.cpp
  Common.cpp
  Custom.cpp
  CustomMethodEdit.cpp
  DOD.cpp
  Eraser.cpp
c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\eraser.cpp(1701): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\eraser.cpp(1701): note: "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\eraser.cpp(1701): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(87): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\eraser.cpp(1705): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\eraser.cpp(1705): note: "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\eraser.cpp(1705): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(87): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\eraser.cpp(1709): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\eraser.cpp(1709): note: "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\eraser.cpp(1709): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(87): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\eraser.cpp(1724): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\eraser.cpp(1724): note: "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\eraser.cpp(1724): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(87): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\eraser.cpp(1730): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\eraser.cpp(1730): note: "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\eraser.cpp(1730): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(87): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\eraser.cpp(1782): warning C4456: “_ctlState”的声明隐藏了上一个本地声明
  c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\eraser.cpp(1752): note: 参见“_ctlState”的声明
c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\eraser.cpp(2480): warning C4457: “filename”的声明隐藏了函数参数
  c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\eraser.cpp(2457): note: 参见“filename”的声明
c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\eraser.cpp(2527): warning C4456: “file”的声明隐藏了上一个本地声明
  c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\eraser.cpp(2493): note: 参见“file”的声明
  FAT.cpp
  File.cpp
c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\file.cpp(420): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\file.cpp(420): note: "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\file.cpp(420): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(87): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
  FileLockResolver.cpp
  FillMemoryWith.cpp
  FirstLast2kb.cpp
  FreeSpace.cpp
c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\freespace.cpp(580): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\freespace.cpp(580): note: "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\freespace.cpp(580): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(87): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
  Gutmann.cpp
  NTFS.cpp
c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\ntfs.cpp(485): warning C4840: 将类 "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>" 作为可变参数函数的参数的不可移植用法
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\ntfs.cpp(485): note: "ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT" 不常用
          with
          [
              _CharType=char
          ]
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\cstringt.h(1026): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>::CStringT”的声明
          with
          [
              _CharType=char
          ]
  c:\users\<USER>\desktop\eraser\5.8.8\eraserdll\ntfs.cpp(485): note: 不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
  c:\program files\microsoft visual studio\2022\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\afxstr.h(87): note: 参见“ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<_CharType>>>”的声明
          with
          [
              _CharType=char
          ]
  OptionPages.cpp
  Options.cpp
  OptionsDlg.cpp
  Pass.cpp
  PassEditDlg.cpp
  Random.cpp
  正在编译...
  ReportDialog.cpp
  RND.cpp
  sboxes.cpp
  Schneier7Pass.cpp
  SecManDlg.cpp
  SecurityManager.cpp
  tiger.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v150\Microsoft.CppBuild.targets(1216,5): warning MSB8012: TargetPath(C:\Users\<USER>\Desktop\Eraser\5.8.8\EraserDll\bin\Win32\Release\EraserDll.dll) 与 Linker 的 OutputFile 属性值(C:\Users\<USER>\Desktop\Eraser\5.8.8\EraserDll\bin\Win32\Release\Eraser.dll)不匹配。这可能导致项目生成不正确。若要更正此问题，请确保 $(OutDir)、$(TargetName) 和 $(TargetExt) 属性值与 %(Link.OutputFile) 中指定的值匹配。
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v150\Microsoft.CppBuild.targets(1218,5): warning MSB8012: TargetName(EraserDll) 与 Linker 的 OutputFile 属性值(Eraser)不匹配。这可能导致项目生成不正确。若要更正此问题，请确保 $(OutDir)、$(TargetName) 和 $(TargetExt) 属性值与 %(Link.OutputFile) 中指定的值匹配。
EraserUI.lib(FlatListCtrl.obj) : warning LNK4075: 忽略“/EDITANDCONTINUE”(由于“/OPT:ICF”规范)
    正在创建库 C:\Users\<USER>\Desktop\Eraser\5.8.8\EraserDll\bin\Win32\Release\EraserDll.lib 和对象 C:\Users\<USER>\Desktop\Eraser\5.8.8\EraserDll\bin\Win32\Release\EraserDll.exp
LINK : warning LNK4098: 默认库“mfc140d.lib”与其他库的使用冲突；请使用 /NODEFAULTLIB:library
LINK : warning LNK4098: 默认库“mfcs140d.lib”与其他库的使用冲突；请使用 /NODEFAULTLIB:library
LINK : warning LNK4098: 默认库“msvcrtd.lib”与其他库的使用冲突；请使用 /NODEFAULTLIB:library
Shared.lib(UserInfo.obj) : error LNK2001: 无法解析的外部符号 __imp___CrtDbgReport
EraserUI.lib(FlatListCtrl.obj) : error LNK2001: 无法解析的外部符号 __imp___CrtDbgReport
EraserUI.lib(FlatHeaderCtrl.obj) : error LNK2001: 无法解析的外部符号 __imp___CrtDbgReport
Shared.lib(SeException.obj) : error LNK2001: 无法解析的外部符号 __imp___CrtDbgReport
Shared.lib(key.obj) : error LNK2001: 无法解析的外部符号 __imp___CrtDbgReport
Shared.lib(SeException.obj) : error LNK2001: 无法解析的外部符号 __imp___CrtDbgReportW
Shared.lib(key.obj) : error LNK2001: 无法解析的外部符号 __imp___CrtDbgReportW
Shared.lib(UserInfo.obj) : error LNK2001: 无法解析的外部符号 __imp___CrtDbgReportW
Shared.lib(stdafx.obj) : error LNK2001: 无法解析的外部符号 __imp___CrtDbgReportW
EraserUI.lib(FlatListCtrl.obj) : error LNK2001: 无法解析的外部符号 __imp___CrtDbgReportW
EraserUI.lib(InPlaceEdit.obj) : error LNK2001: 无法解析的外部符号 __imp___CrtDbgReportW
EraserUI.lib(FlatHeaderCtrl.obj) : error LNK2001: 无法解析的外部符号 __imp___CrtDbgReportW
EraserUI.lib(stdafx.obj) : error LNK2001: 无法解析的外部符号 __imp___CrtDbgReportW
C:\Users\<USER>\Desktop\Eraser\5.8.8\EraserDll\bin\Win32\Release\Eraser.dll : fatal error LNK1120: 2 个无法解析的外部命令
