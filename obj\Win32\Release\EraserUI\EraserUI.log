﻿C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v150\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  stdafx.cpp
c:\users\<USER>\desktop\eraser\src\eraserui\stdafx.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  AlphaImageList.cpp
  AlphaToolBar.cpp
  DirDialog.cpp
c:\users\<USER>\desktop\eraser\src\eraserui\dirdialog.cpp(1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\users\<USER>\desktop\eraser\src\eraserui\dirdialog.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  DriveCombo.cpp
c:\users\<USER>\desktop\eraser\src\eraserui\drivecombo.cpp(1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\users\<USER>\desktop\eraser\src\eraserui\drivecombo.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  FileDialogEx.cpp
  FileTreeCtrl.cpp
c:\users\<USER>\desktop\eraser\src\eraserui\filetreectrl.cpp(2177): warning C4456: “pItem”的声明隐藏了上一个本地声明
  c:\users\<USER>\desktop\eraser\src\eraserui\filetreectrl.cpp(2117): note: 参见“pItem”的声明
c:\users\<USER>\desktop\eraser\src\eraserui\filetreectrl.cpp(2341): warning C4457: “sPath”的声明隐藏了函数参数
  c:\users\<USER>\desktop\eraser\src\eraserui\filetreectrl.cpp(2300): note: 参见“sPath”的声明
c:\users\<USER>\desktop\eraser\src\eraserui\filetreectrl.cpp(2359): warning C4457: “sPath”的声明隐藏了函数参数
  c:\users\<USER>\desktop\eraser\src\eraserui\filetreectrl.cpp(2300): note: 参见“sPath”的声明
c:\users\<USER>\desktop\eraser\src\eraserui\filetreectrl.cpp(2374): warning C4457: “sPath”的声明隐藏了函数参数
  c:\users\<USER>\desktop\eraser\src\eraserui\filetreectrl.cpp(2300): note: 参见“sPath”的声明
c:\users\<USER>\desktop\eraser\src\eraserui\filetreectrl.cpp(2392): warning C4457: “sPath”的声明隐藏了函数参数
  c:\users\<USER>\desktop\eraser\src\eraserui\filetreectrl.cpp(2300): note: 参见“sPath”的声明
  FitFileNameToScrn.cpp
c:\users\<USER>\desktop\eraser\src\eraserui\fitfilenametoscrn.cpp(1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  FlatHeaderCtrl.cpp
c:\users\<USER>\desktop\eraser\src\eraserui\flatheaderctrl.cpp(879): warning C4457: “point”的声明隐藏了函数参数
  c:\users\<USER>\desktop\eraser\src\eraserui\flatheaderctrl.cpp(837): note: 参见“point”的声明
  FlatListCtrl.cpp
c:\users\<USER>\desktop\eraser\src\eraserui\flatlistctrl.cpp(1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\users\<USER>\desktop\eraser\src\eraserui\inplaceedit.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  FlatListView.cpp
c:\users\<USER>\desktop\eraser\src\eraserui\flatlistview.cpp(1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\users\<USER>\desktop\eraser\src\eraserui\inplaceedit.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\users\<USER>\desktop\eraser\src\eraserui\flatlistview.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  GfxGroupEdit.cpp
c:\users\<USER>\desktop\eraser\src\droptargetwnd.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  GfxOutBarCtrl.cpp
c:\users\<USER>\desktop\eraser\src\eraserui\gfxoutbarctrl.cpp(1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\users\<USER>\desktop\eraser\src\droptargetwnd.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\users\<USER>\desktop\eraser\src\eraserui\gfxpopupmenu.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\users\<USER>\desktop\eraser\src\eraserui\gfxoutbarctrl.cpp(1013): warning C4456: “rc”的声明隐藏了上一个本地声明
  c:\users\<USER>\desktop\eraser\src\eraserui\gfxoutbarctrl.cpp(897): note: 参见“rc”的声明
c:\users\<USER>\desktop\eraser\src\eraserui\gfxoutbarctrl.cpp(1023): warning C4456: “rc”的声明隐藏了上一个本地声明
  c:\users\<USER>\desktop\eraser\src\eraserui\gfxoutbarctrl.cpp(897): note: 参见“rc”的声明
c:\users\<USER>\desktop\eraser\src\eraserui\gfxoutbarctrl.cpp(1658): warning C4457: “rc”的声明隐藏了函数参数
  c:\users\<USER>\desktop\eraser\src\eraserui\gfxoutbarctrl.cpp(1601): note: 参见“rc”的声明
c:\users\<USER>\desktop\eraser\src\eraserui\gfxoutbarctrl.cpp(2366): warning C4458: “iFolderHeight”的声明隐藏了类成员
  c:\users\<USER>\desktop\eraser\src\eraserui\gfxoutbarctrl.h(62): note: 参见“CGfxOutBarCtrl::iFolderHeight”的声明
c:\users\<USER>\desktop\eraser\src\eraserui\gfxoutbarctrl.cpp(2402): warning C4458: “iFolderHeight”的声明隐藏了类成员
  c:\users\<USER>\desktop\eraser\src\eraserui\gfxoutbarctrl.h(62): note: 参见“CGfxOutBarCtrl::iFolderHeight”的声明
  GfxPopupMenu.cpp
c:\users\<USER>\desktop\eraser\src\eraserui\gfxpopupmenu.cpp(1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\users\<USER>\desktop\eraser\src\eraserui\gfxpopupmenu.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  GfxSplitterWnd.cpp
c:\users\<USER>\desktop\eraser\src\eraserui\gfxsplitterwnd.cpp(284): warning C4456: “cx”的声明隐藏了上一个本地声明
  c:\users\<USER>\desktop\eraser\src\eraserui\gfxsplitterwnd.cpp(251): note: 参见“cx”的声明
c:\users\<USER>\desktop\eraser\src\eraserui\gfxsplitterwnd.cpp(301): warning C4456: “cy”的声明隐藏了上一个本地声明
  c:\users\<USER>\desktop\eraser\src\eraserui\gfxsplitterwnd.cpp(252): note: 参见“cy”的声明
c:\users\<USER>\desktop\eraser\src\eraserui\gfxsplitterwnd.cpp(314): warning C4456: “cx”的声明隐藏了上一个本地声明
  c:\users\<USER>\desktop\eraser\src\eraserui\gfxsplitterwnd.cpp(251): note: 参见“cx”的声明
c:\users\<USER>\desktop\eraser\src\eraserui\gfxsplitterwnd.cpp(318): warning C4456: “cy”的声明隐藏了上一个本地声明
  c:\users\<USER>\desktop\eraser\src\eraserui\gfxsplitterwnd.cpp(252): note: 参见“cy”的声明
c:\users\<USER>\desktop\eraser\src\eraserui\gfxsplitterwnd.cpp(329): warning C4390: “;”: 找到空的受控语句；这是否是有意的?
  HyperLink.cpp
  InfoBar.cpp
  InPlaceEdit.cpp
c:\users\<USER>\desktop\eraser\src\eraserui\inplaceedit.cpp(1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\users\<USER>\desktop\eraser\src\eraserui\inplaceedit.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  Masked.cpp
  NewDialog.cpp
  ProgressBar.cpp
  正在编译...
  ShellPidl.cpp
c:\users\<USER>\desktop\eraser\src\eraserui\shellpidl.cpp(1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\users\<USER>\desktop\eraser\src\eraserui\shellpidl.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  TimeOutMessageBox.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v150\Microsoft.CppBuild.targets(1397,5): warning MSB8012: TargetPath(C:\Users\<USER>\Desktop\Eraser\src\bin\Win32\Release\EraserUI.lib) 与 Library 的 OutputFile 属性值(C:\Users\<USER>\Desktop\Eraser\src\Lib\OutR\EraserUI.lib)不匹配。这可能导致项目生成不正确。若要更正此问题，请确保 $(OutDir)、$(TargetName) 和 $(TargetExt) 属性值与 %(Lib.OutputFile) 中指定的值匹配。
LINK : warning LNK4068: 未指定 /MACHINE；默认设置为 X86
  EraserUI.vcxproj -> C:\Users\<USER>\Desktop\Eraser\src\bin\Win32\Release\EraserUI.lib
