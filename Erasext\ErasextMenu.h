﻿// ErasextMenu.h
#ifndef ERASEXTMENU_H
#define ERASEXTMENU_H

/////////////////////////////////////////////////////////////////////////////
// CErasextMenu command target

class CErasextMenu : public CCmdTarget
{
    DECLARE_DYNCREATE(CErasextMenu)

    CErasextMenu();           // protected constructor used by dynamic creation

// Attributes
public:

// Operations
public:
    BOOL MoveFileList(CWnd *pParent, CStringArray& saList, CStringArray& saFolders,
                      CStringList& strlSource, LPCTSTR szDestination);

// Overrides
    // ClassWizard generated virtual function overrides
    //{{AFX_VIRTUAL(CErasextMenu)
    public:
    virtual void OnFinalRelease();
    //}}AFX_VIRTUAL

// Implementation
private:
	void getstr_handle_erase(UINT nType, CString& );
	void getstr_handle_move(UINT nType, CString& );
protected:
    BOOL        m_bNT;
    DWORD       m_dwItems;
    DWORD       m_dwDirectories;
    BOOL        m_bUseFiles;

    BOOL        m_bDragMenu;
    TCHAR       m_szDropTarget[MAX_PATH + 2];
	
    CStringArray m_saData;
    CStringArray m_saFolders;

    virtual ~CErasextMenu();


    // Generated message map functions
    //{{AFX_MSG(CErasextMenu)
        // NOTE - the ClassWizard will add and remove member functions here.
    //}}AFX_MSG

    DECLARE_MESSAGE_MAP()
    // Generated OLE dispatch map functions
    //{{AFX_DISPATCH(CErasextMenu)
        // NOTE - the ClassWizard will add and remove member functions here.
    //}}AFX_DISPATCH
    DECLARE_DISPATCH_MAP()
    DECLARE_INTERFACE_MAP()

    DECLARE_OLECREATE(CErasextMenu)

    // IContextMenu Interface 
	BEGIN_INTERFACE_PART(MenuExt, IContextMenu)
        STDMETHOD(QueryContextMenu)(HMENU hMenu, UINT nIndex, UINT idCmdFirst,
            UINT idCmdLast, UINT uFlags);
        STDMETHOD(InvokeCommand)(LPCMINVOKECOMMANDINFO lpici);
        STDMETHOD(GetCommandString)(UINT_PTR  idCmd, UINT nType, UINT* pnReserved,
            LPSTR lpszName, UINT nMax);
    END_INTERFACE_PART(MenuExt)

    // IShellExtInit interface
    BEGIN_INTERFACE_PART(ShellInit, IShellExtInit)
        STDMETHOD(Initialize)(LPCITEMIDLIST pidlFolder, LPDATAOBJECT lpdobj,
            HKEY hkeyProgID);
    END_INTERFACE_PART(ShellInit)
};


/////////////////////////////////////////////////////////////////////////////

#endif
