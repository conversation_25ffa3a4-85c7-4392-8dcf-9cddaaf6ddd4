﻿<!DOCTYPE html>
<!-- saved from url=(0014)about:internet -->
 <html xmlns:msxsl="urn:schemas-microsoft-com:xslt"><head><meta content="en-us" http-equiv="Content-Language" /><meta content="text/html; charset=utf-16" http-equiv="Content-Type" /><title _locID="ConversionReport0">
          迁移报告
        </title><style> 
                    /* Body style, for the entire document */
                    body
                    {
                        background: #F3F3F4;
                        color: #1E1E1F;
                        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
                        padding: 0;
                        margin: 0;
                    }

                    /* Header1 style, used for the main title */
                    h1
                    {
                        padding: 10px 0px 10px 10px;
                        font-size: 21pt;
                        background-color: #E2E2E2;
                        border-bottom: 1px #C1C1C2 solid; 
                        color: #201F20;
                        margin: 0;
                        font-weight: normal;
                    }

                    /* Header2 style, used for "Overview" and other sections */
                    h2
                    {
                        font-size: 18pt;
                        font-weight: normal;
                        padding: 15px 0 5px 0;
                        margin: 0;
                    }

                    /* Header3 style, used for sub-sections, such as project name */
                    h3
                    {
                        font-weight: normal;
                        font-size: 15pt;
                        margin: 0;
                        padding: 15px 0 5px 0;
                        background-color: transparent;
                    }

                    /* Color all hyperlinks one color */
                    a
                    {
                        color: #1382CE;
                    }

                    /* Table styles */ 
                    table
                    {
                        border-spacing: 0 0;
                        border-collapse: collapse;
                        font-size: 10pt;
                    }

                    table th
                    {
                        background: #E7E7E8;
                        text-align: left;
                        text-decoration: none;
                        font-weight: normal;
                        padding: 3px 6px 3px 6px;
                    }

                    table td
                    {
                        vertical-align: top;
                        padding: 3px 6px 5px 5px;
                        margin: 0px;
                        border: 1px solid #E7E7E8;
                        background: #F7F7F8;
                    }

                    /* Local link is a style for hyperlinks that link to file:/// content, there are lots so color them as 'normal' text until the user mouse overs */
                    .localLink
                    {
                        color: #1E1E1F;
                        background: #EEEEED;
                        text-decoration: none;
                    }

                    .localLink:hover
                    {
                        color: #1382CE;
                        background: #FFFF99;
                        text-decoration: none;
                    }

                    /* Center text, used in the over views cells that contain message level counts */ 
                    .textCentered
                    {
                        text-align: center;
                    }

                    /* The message cells in message tables should take up all avaliable space */
                    .messageCell
                    {
                        width: 100%;
                    }

                    /* Padding around the content after the h1 */ 
                    #content 
                    {
	                    padding: 0px 12px 12px 12px; 
                    }

                    /* The overview table expands to width, with a max width of 97% */ 
                    #overview table
                    {
                        width: auto;
                        max-width: 75%; 
                    }

                    /* The messages tables are always 97% width */
                    #messages table
                    {
                        width: 97%;
                    }

                    /* All Icons */
                    .IconSuccessEncoded, .IconInfoEncoded, .IconWarningEncoded, .IconErrorEncoded
                    {
                        min-width:18px;
                        min-height:18px; 
                        background-repeat:no-repeat;
                        background-position:center;
                    }

                    /* Success icon encoded */
                    .IconSuccessEncoded
                    {
                        /* Note: Do not delete the comment below. It is used to verify the correctness of the encoded image resource below before the product is released */
                        /* [---XsltValidateInternal-Base64EncodedImage:IconSuccess#Begin#background-image: url(data:image/png;base64,#Separator#);#End#] */
                        background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAA7EAAAOxAGVKw4bAAABcElEQVR4Xq2TsUsCURzHv15g8ZJcBWlyiYYgCIWcb9DFRRwMW5TA2c0/QEFwFkxxUQdxVlBwCYWOi6IhWgQhBLHJUCkhLr/BW8S7gvrAg+N+v8/v+x68Z8MGy+XSCyABQAXgBgHGALoASkIIDWSLeLBetdHryMjd5IxQPWT4rn1c/P7+xxp72Cs9m5SZ0Bq2vPnbPFafK2zDvmNHypdC0BPkLlQhxJsCAhQoZwdZU5mwxh720qGo8MzTxTTKZDPCx2HoVzp6lz0Q9tKhyx0kGs8Ny+TkWRKk8lCROwEduhyg9l/6lunOPSfmH3NUH6uQ0KHLAe7JYvJjevm+DAMGJHToKtigE+vwvIidxLamb8IBY9e+C5LiXREkfho3TSd06HJA13/oh6T51MTsfQbHrsMynQ5dDihFjiK8JJAU9AKIWTp76dCVN7HWHrajmUEGvyF9nkbAE6gLIS7kTUyuf2gscLoJrElZo/Mvj+nPz/kLTmfnEwP3tB0AAAAASUVORK5CYII=);
                    }

                    /* Information icon encoded */
                    .IconInfoEncoded
                    {
                        /* Note: Do not delete the comment below. It is used to verify the correctness of the encoded image resource below before the product is released */
                        /* [---XsltValidateInternal-Base64EncodedImage:IconInformation#Begin#background-image: url(data:image/png;base64,#Separator#);#End#] */
                        background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABHElEQVR4Xs2TsUoDQRRF7wwoziokjZUKadInhdhukR9YP8DMX1hYW+QvdsXa/QHBbcXC7W0CamWTQnclFutceIQJwwaWNLlwm5k5d94M76mmaeCrrmsLYOocY12FcxZFUeozCqKqqgYA8uevv1H6VuPxcwlfk5N92KHBxfFeCSAxxswlYAW/Xr989x/mv9gkhtyMDhcAxgzRsp7flj8B/HF1RsMXq+NZMkopaHe7lbKxQUEIGbKsYNoGn969060hZBkQex/W8oRQwsQaW2o3Ago2SVcJUzAgY3N0lTCZZm+zPS8HB51gMmS1DEYyOz9acKO1D8JWTlafKIMxdhvlfdyT94Vv5h7P8Ky7nQzACmhvKq3zk3PjW9asz9D/1oigecsioooAAAAASUVORK5CYII=);
                    }

                    /* Warning icon encoded */
                    .IconWarningEncoded
                    {
                        /* Note: Do not delete the comment below. It is used to verify the correctness of the encoded image resource below before the product is released */
                        /* [---XsltValidateInternal-Base64EncodedImage:IconWarning#Begin#background-image: url(data:image/png;base64,#Separator#);#End#] */
                        background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAAx0lEQVR4XpWSMQ7CMAxFf4xAyBMLCxMrO8dhaBcuwdCJS3RJBw7SA/QGTCxdWJgiQYWKXJWKIXHIlyw5lqr34tQgEOdcBsCOx5yZK3hCCKdYXneQkh4pEfqzLfu+wVDSyyzFoJjfz9NB+pAF+eizx2Vruts0k15mPgvS6GYvpVtQhB61IB/dk6AF6fS4Ben0uIX5odtFe8Q/eW1KvFeH4e8khT6+gm5B+t3juyDt7n0jpe+CANTd+oTUjN/U3yVaABnSUjFz/gFq44JaVSCXeQAAAABJRU5ErkJggg==);
                    }

                    /* Error icon encoded */
                    .IconErrorEncoded
                    {
                        /* Note: Do not delete the comment below. It is used to verify the correctness of the encoded image resource below before the product is released */
                        /* [---XsltValidateInternal-Base64EncodedImage:IconError#Begin#background-image: url(data:image/png;base64,#Separator#);#End#] */
                        background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABQElEQVR4XqWTvUoEQRCE6wYPZUA80AfwAQz23uCMjA7MDRQEIzPBVEyNTQUFIw00vcQTTMzuAh/AxEQQT8HF/3G/oGGnEUGuoNnd6qoZuqltyKEsyzVJq5I6rnUp6SjGeGhESikzzlc1eL7opfuVbrqbU1Zw9NCgtQMaZpY0eNnaaL2fHusvTK5vKu7sjSS1Y4y3QUA6K3e3Mau5UFDyMP7tYF9o8cAHZv68vipoIJg971PZIZ5HiwdvYGGvFVFHmGmZ2MxwmQYPXubPl9Up0tfoMQGetXd6mRbvhBw+boZ6WF7Mbv1+GsHRk0fQmPAH1GfmZirbCfDJ61tw3Px8/8pZsPAG4jlVhcPgZ7adwNWBB68lkRQWFiTgFlbnLY3DGGM7izIJIyT/jjIvEJw6fdJTc6krDzh6aMwMP9bvDH4ADSsa9uSWVJkAAAAASUVORK5CYII=);
                    }
                 </style><script type="text/javascript" language="javascript"> 
          
            // Startup 
            // Hook up the the loaded event for the document/window, to linkify the document content
            var startupFunction = function() { linkifyElement("messages"); };
            
            if(window.attachEvent)
            {
              window.attachEvent('onload', startupFunction);
            }
            else if (window.addEventListener) 
            {
              window.addEventListener('load', startupFunction, false);
            }
            else 
            {
              document.addEventListener('load', startupFunction, false);
            } 
            
            // Toggles the visibility of table rows with the specified name 
            function toggleTableRowsByName(name)
            {
               var allRows = document.getElementsByTagName('tr');
               for (i=0; i < allRows.length; i++)
               {
                  var currentName = allRows[i].getAttribute('name');
                  if(!!currentName && currentName.indexOf(name) == 0)
                  {
                      var isVisible = allRows[i].style.display == ''; 
                      isVisible ? allRows[i].style.display = 'none' : allRows[i].style.display = '';
                  }
               }
            }
            
            function scrollToFirstVisibleRow(name) 
            {
               var allRows = document.getElementsByTagName('tr');
               for (i=0; i < allRows.length; i++)
               {
                  var currentName = allRows[i].getAttribute('name');
                  var isVisible = allRows[i].style.display == ''; 
                  if(!!currentName && currentName.indexOf(name) == 0 && isVisible)
                  {
                     allRows[i].scrollIntoView(true); 
                     return true; 
                  }
               }
               
               return false;
            }
            
            // Linkifies the specified text content, replaces candidate links with html links 
            function linkify(text)
            {
                 if(!text || 0 === text.length)
                 {
                     return text; 
                 }

                 // Find http, https and ftp links and replace them with hyper links 
                 var urlLink = /(http|https|ftp)\:\/\/[a-zA-Z0-9\-\.]+(:[a-zA-Z0-9]*)?\/?([a-zA-Z0-9\-\._\?\,\/\\\+&%\$#\=~;\{\}])*/gi;
                 
                 return text.replace(urlLink, '<a href="$&">$&</a>') ;
            }
            
            // Linkifies the specified element by ID
            function linkifyElement(id)
            {
                var element = document.getElementById(id);
                if(!!element)
                {
                  element.innerHTML = linkify(element.innerHTML); 
                }
            }
            
            function ToggleMessageVisibility(projectName)
            {
              if(!projectName || 0 === projectName.length)
              {
                return; 
              }
              
              toggleTableRowsByName("MessageRowClass" + projectName);
              toggleTableRowsByName('MessageRowHeaderShow' + projectName);
              toggleTableRowsByName('MessageRowHeaderHide' + projectName); 
            }
            
            function ScrollToFirstVisibleMessage(projectName)
            {
              if(!projectName || 0 === projectName.length)
              {
                return; 
              }
              
              // First try the 'Show messages' row
              if(!scrollToFirstVisibleRow('MessageRowHeaderShow' + projectName))
              {
                // Failed to find a visible row for 'Show messages', try an actual message row 
                scrollToFirstVisibleRow('MessageRowClass' + projectName); 
              }
            }
           </script></head><body><h1 _locID="ConversionReport">
          迁移报告 - </h1><div id="content"><h2 _locID="OverviewTitle">概述</h2><div id="overview"><table><tr><th></th><th _locID="ProjectTableHeader">项目</th><th _locID="PathTableHeader">路径</th><th _locID="ErrorsTableHeader">错误</th><th _locID="WarningsTableHeader">警告</th><th _locID="MessagesTableHeader">消息</th></tr><tr><td class="IconWarningEncoded" /><td><strong><a href="#EraserUI">EraserUI</a></strong></td><td>EraserUI.vcproj</td><td class="textCentered"><a>0</a></td><td class="textCentered"><a href="#EraserUIWarning">3</a></td><td class="textCentered"><a href="#" onclick="ScrollToFirstVisibleMessage('EraserUI'); return false;">37</a></td></tr></table></div><h2 _locID="SolutionAndProjectsTitle">解决方案和项目</h2><div id="messages"><a name="EraserUI" /><h3>EraserUI</h3><table><tr id="EraserUIHeaderRow"><th></th><th class="messageCell" _locID="MessageTableHeader">消息</th></tr><tr name="WarningRowClassEraserUI"><td class="IconWarningEncoded"><a name="EraserUIWarning" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Attribute 'ProjectType' of 'Deployment' is not supported in this version and has been removed during conversion.</span></td></tr><tr name="WarningRowClassEraserUI"><td class="IconWarningEncoded"><a name="EraserUIWarning" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Attribute 'Version' of 'Deployment' is not supported in this version and has been removed during conversion.</span></td></tr><tr name="WarningRowClassEraserUI"><td class="IconWarningEncoded"><a name="EraserUIWarning" /></td><td class="messageCell"><strong>EraserUI.vcproj:
        </strong><span>VCWebServiceProxyGeneratorTool is no longer supported. The tool has been removed from your project settings.</span></td></tr><tr name="MessageRowHeaderShowEraserUI"><td class="IconInfoEncoded" /><td class="messageCell"><a _locID="ShowAdditionalMessages" href="#" name="EraserUIMessage" onclick="ToggleMessageVisibility('EraserUI'); return false;">
          显示 37 其他消息
        </a></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>EraserUI.vcproj:
        </strong><span>Converting project file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\EraserUI\EraserUI.vcproj'.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Converting propertysheet file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.vsprops' for configuration 'Debug|Win32'.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Done converting to new property sheet file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.vsprops' for configuration 'Debug|Win32'.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Converting propertysheet file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.vsprops' for configuration 'Debug|x64'.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Converting configuration 'Debug|x64', settings file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.props' already exists and will not attempt to overwrite it.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Done converting to new property sheet file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.vsprops' for configuration 'Debug|x64'.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Converting propertysheet file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.vsprops' for configuration 'Release|Win32'.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Converting configuration 'Release|Win32', settings file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.props' already exists and will not attempt to overwrite it.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Done converting to new property sheet file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.vsprops' for configuration 'Release|Win32'.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Converting propertysheet file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.vsprops' for configuration 'Release|x64'.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Converting configuration 'Release|x64', settings file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.props' already exists and will not attempt to overwrite it.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Done converting to new property sheet file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.vsprops' for configuration 'Release|x64'.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Converting propertysheet file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.vsprops' for configuration 'Standalone Release|Win32'.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Converting configuration 'Standalone Release|Win32', settings file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.props' already exists and will not attempt to overwrite it.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Done converting to new property sheet file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.vsprops' for configuration 'Standalone Release|Win32'.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Converting propertysheet file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.vsprops' for configuration 'Standalone Release|x64'.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Converting configuration 'Standalone Release|x64', settings file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.props' already exists and will not attempt to overwrite it.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Done converting to new property sheet file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.vsprops' for configuration 'Standalone Release|x64'.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Converting propertysheet file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.vsprops' for configuration 'Release_Unicode|Win32'.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Converting configuration 'Release_Unicode|Win32', settings file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.props' already exists and will not attempt to overwrite it.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Done converting to new property sheet file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.vsprops' for configuration 'Release_Unicode|Win32'.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Converting propertysheet file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.vsprops' for configuration 'Release_Unicode|x64'.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Converting configuration 'Release_Unicode|x64', settings file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.props' already exists and will not attempt to overwrite it.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Done converting to new property sheet file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.vsprops' for configuration 'Release_Unicode|x64'.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Converting propertysheet file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.vsprops' for configuration 'Debug_Unicode|Win32'.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Converting configuration 'Debug_Unicode|Win32', settings file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.props' already exists and will not attempt to overwrite it.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Done converting to new property sheet file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.vsprops' for configuration 'Debug_Unicode|Win32'.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Converting propertysheet file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.vsprops' for configuration 'Debug_Unicode|x64'.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Converting configuration 'Debug_Unicode|x64', settings file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.props' already exists and will not attempt to overwrite it.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Done converting to new property sheet file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.vsprops' for configuration 'Debug_Unicode|x64'.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Converting propertysheet file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.vsprops' for configuration 'Standalone Release Unicode|Win32'.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Converting configuration 'Standalone Release Unicode|Win32', settings file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.props' already exists and will not attempt to overwrite it.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Done converting to new property sheet file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.vsprops' for configuration 'Standalone Release Unicode|Win32'.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Converting propertysheet file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.vsprops' for configuration 'Standalone Release Unicode|x64'.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Converting configuration 'Standalone Release Unicode|x64', settings file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.props' already exists and will not attempt to overwrite it.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>..\Deployment.vsprops:
        </strong><span>Done converting to new property sheet file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\Deployment.vsprops' for configuration 'Standalone Release Unicode|x64'.</span></td></tr><tr name="MessageRowClassEraserUI" style="display: none"><td class="IconInfoEncoded"><a name="EraserUIMessage" /></td><td class="messageCell"><strong>EraserUI.vcproj:
        </strong><span>Done converting to new project file 'C:\Users\<USER>\Desktop\Eraser\5.8.8\EraserUI\EraserUI.vcxproj'.</span></td></tr><tr style="display: none" name="MessageRowHeaderHideEraserUI"><td class="IconInfoEncoded" /><td class="messageCell"><a _locID="HideAdditionalMessages" href="#" name="EraserUIMessage" onclick="ToggleMessageVisibility('EraserUI'); return false;">
          隐藏 37 其他消息
        </a></td></tr></table></div></div></body></html>