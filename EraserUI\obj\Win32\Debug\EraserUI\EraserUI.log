﻿C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v150\Microsoft.Cpp.Common.props(209,5): warning MSB4211: 正在首次将属性“VC_ExecutablePath_x64_x64”设置为某个值，但已在“C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v170\Microsoft.Cpp.Analysis.props (23,5)”处使用了该属性。
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v150\Microsoft.CppBuild.targets(948,5): warning MSB4211: 正在首次将属性“GetTargetPathDependsOn”设置为某个值，但已在“C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\amd64\Microsoft.Common.CurrentVersion.targets (2243,5)”处使用了该属性。
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v150\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
LINK : warning LNK4068: 未指定 /MACHINE；默认设置为 X86
  EraserUI.vcxproj -> C:\Users\<USER>\Desktop\Eraser\5.8.8\EraserUI\bin\Win32\Debug\EraserUI.lib
