﻿// ConfirmReplaceDlg.h

#if !defined(AFX_CONFIRMREPLACEDLG_H__E1E50051_2FC1_11D3_8212_00105AAF62C4__INCLUDED_)
#define AFX_CONFIRMREPLACEDLG_H__E1E50051_2FC1_11D3_8212_00105AAF62C4__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

/////////////////////////////////////////////////////////////////////////////
// CConfirmReplaceDlg dialog

class CConfirmReplaceDlg : public CDialog
{
// Construction
public:
    CConfirmReplaceDlg(CWnd* pParent = NULL);   // standard constructor
    void SetExisting(LPCTSTR sz)    { m_strExistingFile = sz; }
    void SetSource(LPCTSTR sz)      { m_strSourceFile = sz; }

    BOOL ApplyToAll()               { return m_bApplyToAll; }

// Dialog Data
    //{{AFX_DATA(CConfirmReplaceDlg)
    enum { IDD = IDD_DIALOG_REPLACE };
    CStatic m_stIconSource;
    CStatic m_stIconExisting;
    CString m_strSource;
    CString m_strExisting;
    CString m_strHeader;
    //}}AFX_DATA


// Overrides
    // ClassWizard generated virtual function overrides
    //{{AFX_VIRTUAL(CConfirmReplaceDlg)
    protected:
    virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
    //}}AFX_VIRTUAL

// Implementation
protected:

    BOOL FormatInfo(LPCTSTR szFile, CString& strInfo);
    BOOL GetFileSizeAndModifiedData(LPCTSTR szFile, ULARGE_INTEGER& uiSize, COleDateTime& odtModified);

    CString m_strExistingFile;
    CString m_strSourceFile;

    BOOL m_bApplyToAll;

    // Generated message map functions
    //{{AFX_MSG(CConfirmReplaceDlg)
    virtual BOOL OnInitDialog();
	afx_msg void OnNoToAll();
	afx_msg void OnYesToAll();
	//}}AFX_MSG
    DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_CONFIRMREPLACEDLG_H__E1E50051_2FC1_11D3_8212_00105AAF62C4__INCLUDED_)
