<?xml version="1.0" encoding="windows-1251"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="9.00"
	Name="Erasext"
	ProjectGUID="{E3DBF6A6-475B-4BA8-AE20-73655A7D42D9}"
	RootNamespace="Erasext"
	Keyword="MFCProj"
	TargetFrameworkVersion="131072"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
		<Platform
			Name="x64"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="2"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops;..\Deployment.vsprops"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="1"
				TypeLibraryName="$(SolutionDir)\lib\$(ConfigurationName)\Erasext.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				PreprocessorDefinitions="NDEBUG"
				StringPooling="true"
				ExceptionHandling="2"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile="$(IntDir)\Erasext.pch"
				AssemblerListingLocation=""
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"
				AdditionalIncludeDirectories="&quot;$(OutDir)&quot;"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="EraserUI.lib Shared.lib"
				OutputFile="$(OutDir)\Erasext.dll"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="&quot;$(OUTDIR)&quot;"
				ModuleDefinitionFile=".\Erasext.def"
				GenerateDebugInformation="true"
				SubSystem="2"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				SetChecksum="true"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|x64"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="2"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops;..\Deployment.vsprops"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="3"
				TypeLibraryName="$(SolutionDir)\lib\$(ConfigurationName)\Erasext.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				PreprocessorDefinitions="NDEBUG"
				StringPooling="true"
				ExceptionHandling="2"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile="$(IntDir)\Erasext.pch"
				AssemblerListingLocation=""
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"
				AdditionalIncludeDirectories="&quot;$(OutDir)&quot;"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="EraserUI.lib Shared.lib"
				OutputFile="$(OutDir)\Erasext.dll"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="&quot;$(OUTDIR)&quot;"
				ModuleDefinitionFile=".\Erasext.def"
				GenerateDebugInformation="true"
				SubSystem="2"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				SetChecksum="true"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="17"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="2"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops;..\Deployment.vsprops"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="_DEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="1"
				TypeLibraryName="$(SolutionDir)\lib\$(ConfigurationName)\Erasext.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				MinimalRebuild="true"
				ExceptionHandling="2"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile="$(IntDir)\Erasext.pch"
				AssemblerListingLocation=""
				SuppressStartupBanner="true"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="1033"
				AdditionalIncludeDirectories="&quot;$(OutDir)&quot;"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="EraserUI.lib Shared.lib"
				OutputFile="$(OutDir)\Erasext.dll"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="&quot;$(OUTDIR)&quot;"
				ModuleDefinitionFile=".\Erasext.def"
				GenerateDebugInformation="true"
				SubSystem="2"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug|x64"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="2"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops;..\Deployment.vsprops"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="_DEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="3"
				TypeLibraryName="$(SolutionDir)\lib\$(ConfigurationName)\Erasext.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				MinimalRebuild="true"
				ExceptionHandling="2"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile="$(IntDir)\Erasext.pch"
				AssemblerListingLocation=""
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="1033"
				AdditionalIncludeDirectories="&quot;$(OutDir)&quot;"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="EraserUI.lib Shared.lib"
				OutputFile="$(OutDir)\Erasext.dll"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="&quot;$(OUTDIR)&quot;"
				ModuleDefinitionFile=".\Erasext.def"
				GenerateDebugInformation="true"
				SubSystem="2"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="17"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Standalone Release|Win32"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="2"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops;..\Deployment.vsprops"
			UseOfMFC="1"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="1"
				TypeLibraryName="$(SolutionDir)\lib\$(ConfigurationName)\Erasext.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				PreprocessorDefinitions="NDEBUG;ERASER_STANDALONE"
				StringPooling="true"
				ExceptionHandling="2"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile="$(IntDir)\Erasext.pch"
				AssemblerListingLocation=""
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"
				AdditionalIncludeDirectories="&quot;$(OutDir)&quot;"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="unicows.lib EraserUI.lib Shared.lib"
				OutputFile="$(OutDir)\Erasext.dll"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="&quot;$(OUTDIR)&quot;"
				ModuleDefinitionFile=".\Erasext.def"
				GenerateDebugInformation="true"
				SubSystem="2"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				SetChecksum="true"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Standalone Release|x64"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="2"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops;..\Deployment.vsprops"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="3"
				TypeLibraryName="$(SolutionDir)\lib\$(ConfigurationName)\Erasext.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				PreprocessorDefinitions="NDEBUG;ERASER_STANDALONE"
				StringPooling="true"
				ExceptionHandling="2"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile="$(IntDir)\Erasext.pch"
				AssemblerListingLocation=""
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"
				AdditionalIncludeDirectories="&quot;$(OutDir)&quot;"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="EraserUI.lib Shared.lib"
				OutputFile="$(OutDir)\Erasext.dll"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="&quot;$(OUTDIR)&quot;"
				ModuleDefinitionFile=".\Erasext.def"
				GenerateDebugInformation="true"
				SubSystem="2"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				SetChecksum="true"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="17"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release_Unicode|Win32"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="2"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops;..\Deployment.vsprops"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="1"
				TypeLibraryName="$(SolutionDir)\lib\$(ConfigurationName)\Erasext.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				PreprocessorDefinitions="NDEBUG"
				StringPooling="true"
				ExceptionHandling="2"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile="$(IntDir)\Erasext.pch"
				AssemblerListingLocation=""
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"
				AdditionalIncludeDirectories="&quot;$(OutDir)&quot;"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="unicows.lib EraserUI.lib Shared.lib"
				OutputFile="$(OutDir)\Erasext.dll"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="&quot;$(OUTDIR)&quot;"
				ModuleDefinitionFile=".\Erasext.def"
				GenerateDebugInformation="true"
				SubSystem="2"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				SetChecksum="true"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release_Unicode|x64"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="2"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops;..\Deployment.vsprops"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="3"
				TypeLibraryName="$(SolutionDir)\lib\$(ConfigurationName)\Erasext.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				PreprocessorDefinitions="NDEBUG"
				StringPooling="true"
				ExceptionHandling="2"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile="$(IntDir)\Erasext.pch"
				AssemblerListingLocation=""
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"
				AdditionalIncludeDirectories="&quot;$(OutDir)&quot;"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="EraserUI.lib Shared.lib"
				OutputFile="$(OutDir)\Erasext.dll"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="&quot;$(OUTDIR)&quot;"
				ModuleDefinitionFile=".\Erasext.def"
				GenerateDebugInformation="true"
				SubSystem="2"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				SetChecksum="true"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="17"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug_Unicode|Win32"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="2"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops;..\Deployment.vsprops"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="_DEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="1"
				TypeLibraryName="$(SolutionDir)\lib\$(ConfigurationName)\Erasext.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				MinimalRebuild="true"
				ExceptionHandling="2"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile="$(IntDir)\Erasext.pch"
				AssemblerListingLocation=""
				SuppressStartupBanner="true"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="1033"
				AdditionalIncludeDirectories="&quot;$(OutDir)&quot;"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="unicows.lib EraserUI.lib Shared.lib"
				OutputFile="$(OutDir)\Erasext.dll"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="&quot;$(OUTDIR)&quot;"
				ModuleDefinitionFile=".\Erasext.def"
				GenerateDebugInformation="true"
				SubSystem="2"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug_Unicode|x64"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="2"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops;..\Deployment.vsprops"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="_DEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="3"
				TypeLibraryName="$(SolutionDir)\lib\$(ConfigurationName)\Erasext.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				MinimalRebuild="true"
				ExceptionHandling="2"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile="$(IntDir)\Erasext.pch"
				AssemblerListingLocation=""
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="1033"
				AdditionalIncludeDirectories="&quot;$(OutDir)&quot;"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="EraserUI.lib Shared.lib"
				OutputFile="$(OutDir)\Erasext.dll"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="&quot;$(OUTDIR)&quot;"
				ModuleDefinitionFile=".\Erasext.def"
				GenerateDebugInformation="true"
				SubSystem="2"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="17"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Standalone Release Unicode|Win32"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="2"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops;..\Deployment.vsprops"
			UseOfMFC="1"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="1"
				TypeLibraryName="$(SolutionDir)\lib\$(ConfigurationName)\Erasext.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				PreprocessorDefinitions="NDEBUG;ERASER_STANDALONE"
				StringPooling="true"
				ExceptionHandling="2"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile="$(IntDir)\Erasext.pch"
				AssemblerListingLocation=""
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"
				AdditionalIncludeDirectories="&quot;$(OutDir)&quot;"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="unicows.lib EraserUI.lib Shared.lib"
				OutputFile="$(OutDir)\Erasext.dll"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="&quot;$(OUTDIR)&quot;"
				ModuleDefinitionFile=".\Erasext.def"
				GenerateDebugInformation="true"
				SubSystem="2"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				SetChecksum="true"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Standalone Release Unicode|x64"
			OutputDirectory="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(SolutionDir)obj\$(PlatformName)\$(ConfigurationName)\$(ProjectName)"
			ConfigurationType="2"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops;..\Deployment.vsprops"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="3"
				TypeLibraryName="$(SolutionDir)\lib\$(ConfigurationName)\Erasext.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				PreprocessorDefinitions="NDEBUG;ERASER_STANDALONE"
				StringPooling="true"
				ExceptionHandling="2"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile="$(IntDir)\Erasext.pch"
				AssemblerListingLocation=""
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"
				AdditionalIncludeDirectories="&quot;$(OutDir)&quot;"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="EraserUI.lib Shared.lib"
				OutputFile="$(OutDir)\Erasext.dll"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="&quot;$(OUTDIR)&quot;"
				ModuleDefinitionFile=".\Erasext.def"
				GenerateDebugInformation="true"
				SubSystem="2"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				SetChecksum="true"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="17"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;rc;def;r;odl;hpj;bat;for;f90"
			>
			<File
				RelativePath="ConfirmDialog.cpp"
				>
			</File>
			<File
				RelativePath="ConfirmReplaceDlg.cpp"
				>
			</File>
			<File
				RelativePath="res\Eraser.ico"
				>
			</File>
			<File
				RelativePath="Erasext.cpp"
				>
			</File>
			<File
				RelativePath="Erasext.def"
				>
			</File>
			<File
				RelativePath="Erasext.odl"
				>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCMIDLTool"
						PreprocessorDefinitions=""
						TargetEnvironment="1"
						TypeLibraryName="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)\$(ProjectName).tlb"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCMIDLTool"
						PreprocessorDefinitions=""
						TargetEnvironment="1"
						TypeLibraryName="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)\$(ProjectName).tlb"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCMIDLTool"
						PreprocessorDefinitions=""
						TargetEnvironment="1"
						TypeLibraryName="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)\$(ProjectName).tlb"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCMIDLTool"
						PreprocessorDefinitions=""
						TargetEnvironment="1"
						TypeLibraryName="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)\$(ProjectName).tlb"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Standalone Release|Win32"
					>
					<Tool
						Name="VCMIDLTool"
						PreprocessorDefinitions=""
						TargetEnvironment="1"
						TypeLibraryName="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)\$(ProjectName).tlb"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Standalone Release|x64"
					>
					<Tool
						Name="VCMIDLTool"
						PreprocessorDefinitions=""
						TargetEnvironment="1"
						TypeLibraryName="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)\$(ProjectName).tlb"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release_Unicode|Win32"
					>
					<Tool
						Name="VCMIDLTool"
						PreprocessorDefinitions=""
						TargetEnvironment="1"
						TypeLibraryName="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)\$(ProjectName).tlb"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release_Unicode|x64"
					>
					<Tool
						Name="VCMIDLTool"
						PreprocessorDefinitions=""
						TargetEnvironment="1"
						TypeLibraryName="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)\$(ProjectName).tlb"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug_Unicode|Win32"
					>
					<Tool
						Name="VCMIDLTool"
						PreprocessorDefinitions=""
						TargetEnvironment="1"
						TypeLibraryName="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)\$(ProjectName).tlb"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug_Unicode|x64"
					>
					<Tool
						Name="VCMIDLTool"
						PreprocessorDefinitions=""
						TargetEnvironment="1"
						TypeLibraryName="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)\$(ProjectName).tlb"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Standalone Release Unicode|Win32"
					>
					<Tool
						Name="VCMIDLTool"
						PreprocessorDefinitions=""
						TargetEnvironment="1"
						TypeLibraryName="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)\$(ProjectName).tlb"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Standalone Release Unicode|x64"
					>
					<Tool
						Name="VCMIDLTool"
						PreprocessorDefinitions=""
						TargetEnvironment="1"
						TypeLibraryName="$(SolutionDir)bin\$(PlatformName)\$(ConfigurationName)\$(ProjectName).tlb"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="Erasext.rc"
				>
			</File>
			<File
				RelativePath="ErasextMenu.cpp"
				>
			</File>
			<File
				RelativePath="StdAfx.cpp"
				>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Standalone Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Standalone Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release_Unicode|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release_Unicode|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug_Unicode|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug_Unicode|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Standalone Release Unicode|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Standalone Release Unicode|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="WipeProgDlg.cpp"
				>
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl;fi;fd"
			>
			<File
				RelativePath="ConfirmDialog.h"
				>
			</File>
			<File
				RelativePath="ConfirmReplaceDlg.h"
				>
			</File>
			<File
				RelativePath="Erasext.h"
				>
			</File>
			<File
				RelativePath="ErasextMenu.h"
				>
			</File>
			<File
				RelativePath="StdAfx.h"
				>
			</File>
			<File
				RelativePath="..\version.h"
				>
			</File>
			<File
				RelativePath="WipeProgDlg.h"
				>
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="ico;cur;bmp;dlg;rc2;rct;bin;cnt;rtf;gif;jpg;jpeg;jpe"
			>
			<File
				RelativePath="res\Erasext.rc2"
				>
			</File>
			<File
				RelativePath="res\icon_rep.ico"
				>
			</File>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
