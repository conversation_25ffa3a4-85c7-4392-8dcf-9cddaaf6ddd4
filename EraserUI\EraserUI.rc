// Microsoft Visual C++ generated resource script.
//
#include "resource.h"

#define APSTUDIO_READONLY_SYMBOLS
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 2 resource.
//
#include "afxres.h"

/////////////////////////////////////////////////////////////////////////////
#undef APSTUDIO_READONLY_SYMBOLS

/////////////////////////////////////////////////////////////////////////////
// English (U.S.) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENE)
LANGUAGE 9, 18
#pragma code_page(1252)

#ifdef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// TEXTINCLUDE
//

1 TEXTINCLUDE  
BEGIN
    "resource.h\0"
END

2 TEXTINCLUDE  
BEGIN
    "#include ""afxres.h""\r\n"
    "\0"
END

3 TEXTINCLUDE  
BEGIN
    "\r\n"
    "\0"
END

#endif    // APSTUDIO_INVOKED

#endif    // English (U.S.) resources
/////////////////////////////////////////////////////////////////////////////



#ifndef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 3 resource.
//


/////////////////////////////////////////////////////////////////////////////
#endif    // not APSTUDIO_INVOKED

/////////////////////////////////////////////////////////////////////////////
//
// Dialog
//
IDD_DLGNEW_DIALOG DIALOGEX 0, 0, 267, 226
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_VISIBLE | WS_CAPTION | WS_SYSMENU
EXSTYLE WS_EX_APPWINDOW
CAPTION "Choose File/Folder"
FONT 8, "MS Shell Dlg", 0, 0, 0x1
BEGIN
    DEFPUSHBUTTON   "OK",IDOK,210,205,50,14
    CONTROL         "Tree1",IDC_TREE1,"SysTreeView32",WS_BORDER | WS_TABSTOP,7,7,253,187
END


/////////////////////////////////////////////////////////////////////////////
//
// Menu
//
IDR_TREEFILECTRL_POPUP MENU DISCARDABLE
BEGIN
    POPUP "_POPUP_"
    BEGIN
        MENUITEM "&Open",                       ID_FILE_OPEN
        MENUITEM "&Rename",                     ID_FILE_RENAME
        MENUITEM "&Delete",                     ID_FILE_DELETE
        MENUITEM "&Properties",                 ID_FILE_PROPERTIES
    END
END

/////////////////////////////////////////////////////////////////////////////
//
// Cursor
//
IDR_TREEFILECTRL_DROPCOPY    CURSOR                  "..\\res\\tfdropcopy.cur"
IDR_TREEFILECTRL_NO_DROPCOPY CURSOR                  "..\\res\\tfnodropcopy.cur"
IDR_TREEFILECTRL_NO_DROPMOVE CURSOR                  "..\\res\\tfnodropmove.cur"
IDC_DRAGGING                 CURSOR                  "..\\res\\dragging.cur"
IDC_HANDCUR                  CURSOR                  "..\\res\\icr_hand.cur"
IDC_NODRAGGING               CURSOR                  "..\\res\\nodraggi.cur"

/////////////////////////////////////////////////////////////////////////////
//
// Bitmap
//
IDB_TREEFILECTRL_NETWORK     BITMAP                  "..\\res\\ftnetwork.bmp"
IDB_GFX_MENUCHECK            BITMAP                  "..\\res\\MenuCheck.bmp"
IDB_GFX_MENUCHECK_SELECTED   BITMAP                  "..\\res\\MenuCheckSelected.bmp"
IDR_GFXMENU_TOOLBAR          BITMAP                  "..\\res\\toolbar1.bmp"

/////////////////////////////////////////////////////////////////////////////
//
// Toolbar
//
IDR_GFXMENU_TOOLBAR TOOLBAR  16, 15
BEGIN
    BUTTON      ID_GFX_LARGEICON
    BUTTON      ID_GFX_SMALLICON
END
