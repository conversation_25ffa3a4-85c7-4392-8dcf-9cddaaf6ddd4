﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="AlphaImageList.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="AlphaToolBar.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DirDialog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DriveCombo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="FileDialogEx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="FileTreeCtrl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="FitFileNameToScrn.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="FlatHeaderCtrl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="FlatListCtrl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="FlatListView.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="GfxGroupEdit.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="GfxOutBarCtrl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="GfxPopupMenu.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="GfxSplitterWnd.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="HyperLink.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="InfoBar.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="InPlaceEdit.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Masked.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NewDialog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ProgressBar.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ShellPidl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="stdafx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="TimeOutMessageBox.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="AlphaImageList.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="AlphaToolBar.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DirDialog.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DriveCombo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FileDialogEx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FileTreeCtrl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FitFileNameToScrn.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FlatHeaderCtrl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FlatListCtrl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FlatListView.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="GfxGroupEdit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="GfxOutBarCtrl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="GfxPopupMenu.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="GfxSplitterWnd.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="HyperLink.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="InfoBar.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="InPlaceEdit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Masked.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MemDC.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NewDialog.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ProgressBar.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ShellPidl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="stdafx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="TimeOutMessageBox.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="VisualStyles.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="EraserUI.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
</Project>