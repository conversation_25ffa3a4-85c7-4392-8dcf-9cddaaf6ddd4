﻿//{{NO_DEPENDENCIES}}
// Microsoft Visual C++ generated include file.
// Used by Eraser.rc
//
#define IDC_BUTTON_SAVEAS               3
#define IDS_PROPSHT_CAPTION             105
#define IDS_PASSWDNOTMATCH              106
#define IDD_PAGE_FILES                  107
#define IDS_PASSWDEMPTY                 107
#define IDD_PAGE_FREESPACE              108
#define IDS_ERROR_CLUSTER               113
#define IDS_ERROR_MEMORY                114
#define IDS_ERROR_INTERNAL              115
#define IDS_ERROR_DIRECTORY             116
#define IDS_ERROR_FREESPACE             117
#define IDS_ERROR_DIRENTRIES            118
#define IDS_ERROR_DIRENTRIES_FS         119
#define IDS_ERROR_DIRENTRIES_FAT        120
#define IDS_ERROR_DIRENTRIES_MAXRESTARTS 121
#define IDS_ERROR_NODATA                122
#define IDS_ERROR_DIRECTORY_REMOVE      123
#define IDS_ERROR_DIRENTRIES_LOCK       124
#define IDS_ERROR_TEMPFILE              125
#define IDS_ERROR_ADS                   126
#define IDR_MAINFRAME                   128
#define IDS_METHOD_DELETE               130
#define IDS_METHOD_NOPASSES             131
#define IDS_METHOD_NONESELECTED         132
#define IDI_ICON_OPTIONS                134
#define IDI_ICON_DATA                   138
#define IDD_DIALOG_METHODEDIT           145
#define IDD_DIALOG_PASSEDIT             146
#define IDD_DIALOG_REPORT               147
#define IDD_DIALOG_SEC_MAN              148
#define IDC_RADIO_PSEUDORANDOM          1000
#define IDC_LIST_METHOD                 1021
#define IDC_BUTTON_NEW                  1022
#define IDC_BUTTON_DELETE               1023
#define IDC_BUTTON_EDIT                 1024
#define IDC_STATIC_SELECTED             1025
#define IDC_CHECK_FREESPACE             1026
#define IDC_CHECK_CLUSTERTIPS           1027
#define IDC_CHECK_DIRECTORYENTRIES      1028
#define IDC_BUTTON_ADD                  1030
#define IDC_BUTTON_COPY                 1031
#define IDC_BUTTON_UP                   1032
#define IDC_BUTTON_DOWN                 1033
#define IDC_EDIT_DESCRIPTION            1034
#define IDC_RADIO_PATTERN               1035
#define IDC_CHECK_BYTE2                 1037
#define IDC_CHECK_BYTE3                 1038
#define IDC_CHECK_SHUFFLE               1040
#define IDC_EDIT_BYTE1                  1041
#define IDC_EDIT_BYTE2                  1042
#define IDC_EDIT_BYTE3                  1043
#define IDC_LIST_PASSES                 1044
#define IDC_STATIC_BYTE1                1045
#define IDC_EDIT_PASSES                 1046
#define IDC_SPIN_PASSES                 1047
#define IDC_STATIC_COMPLETION           1048
#define IDC_EDIT_STATISTICS             1049
#define IDC_LIST_ERRORS                 1050
#define IDC_STATIC_FAILURES_HEADER      1051
#define IDC_CHECK_FILECLUSTERTIPS       1052
#define IDC_CHECK_FILENAMES             1053
#define IDC_CHECK_ALTERNATESTREAMS      1054
#define IDC_EDIT_SECMAN_PASSWD          1055
#define IDC_EDIT_SECMAN_PASSWDCONFIRM   1057
#define IDC_STATIC_CONFIRM              1058

// Next default values for new objects
// 
#ifdef APSTUDIO_INVOKED
#ifndef APSTUDIO_READONLY_SYMBOLS
#define _APS_NEXT_RESOURCE_VALUE        149
#define _APS_NEXT_COMMAND_VALUE         32771
#define _APS_NEXT_CONTROL_VALUE         1059
#define _APS_NEXT_SYMED_VALUE           113
#endif
#endif
