﻿#ifndef TIGER_H
#define TIGER_H

/*
** If you use these, you of course know that state is 24 bytes and that
** the block size of tiger_compress is 64 bytes
*/

void tiger_compress(E_PUINT64 block, E_PUINT64 state);

/*
** Tiger needs one-block (64 bytes) work buffer, if you don't provide it,
** one will be allocated for you.
*/

void tiger(E_PUINT64 buffer, E_UINT64 length, E_PUINT64 state, E_PUINT8 work);
void tiger(E_PUINT64 buffer, E_UINT64 length, E_PUINT64 state);

#endif
