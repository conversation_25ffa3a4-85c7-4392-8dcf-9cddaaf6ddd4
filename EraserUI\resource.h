//{{NO_DEPENDENCIES}}
// Microsoft Visual C++ generated include file.
// Used by EraserUI.rc
#define IDR_MAINFRAME                   128 //Global - don't change!

#define IDC_DRAGGING                    130
#define IDC_HANDCUR                     131
#define IDC_NODRAGGING                  132

#define ID_GFX_ANIMATION                110
#define IDR_GFXMENU_TOOLBAR             135
#define IDB_GFX_MENUCHECK               152
#define IDB_GFX_MENUCHECK_SELECTED      154
#define ID_GFX_LARGEICON                32771
#define ID_GFX_SMALLICON                32772
#define ID_GFX_REMOVEITEM               32773
#define ID_GFX_RENAMEITEM               32775

#define IDD_DLGNEW_DIALOG               93
#define IDC_TREE1                       98

#define IDR_TREEFILECTRL_NO_DROPMOVE    94
#define IDR_TREEFILECTRL_NO_DROPCOPY    95
#define IDR_TREEFILECTRL_DROPCOPY       96
#define IDB_TREEFILECTRL_NETWORK        97
#define IDR_TREEFILECTRL_POPUP          114

#define ID_TREEFILECTRL_UPONELEVEL      32778
#define ID_TREEFILECTRL_OPEN            32782
#define ID_TREEFILECTRL_DELETE          32783
#define ID_TREEFILECTRL_RENAME          32784
#define ID_TREEFILECTRL_PROPERTIES      32785
#define ID_TREEFILECTRL_REFRESH         32790
#define ID_TREEFILECTRL_BACK            32803
#define ID_TREEFILECTRL_FORWARD         32804

#define ID_FILE_RENAME                  270
#define ID_FILE_DELETE                  271
#define ID_FILE_PROPERTIES              272

// Next default values for new objects
// 
#ifdef APSTUDIO_INVOKED
#ifndef APSTUDIO_READONLY_SYMBOLS
#define _APS_NEXT_RESOURCE_VALUE        101
#define _APS_NEXT_COMMAND_VALUE         40001
#define _APS_NEXT_CONTROL_VALUE         1001
#define _APS_NEXT_SYMED_VALUE           101
#endif
#endif
