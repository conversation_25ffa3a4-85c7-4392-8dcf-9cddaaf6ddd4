#ifndef __OPTIONSDLG_H__
#define __OPTIONSDLG_H__

#include "OptionPages.h"

/////////////////////////////////////////////////////////////////////////////
// COptionsDlg

class COptionsDlg : public CPropertySheet
{
    DECLARE_DYNAMIC(COptionsDlg)

// Construction
public:
    COptionsDlg(CWnd* pWndParent = NULL);

// Attributes
public:
    COptionsForFiles m_pgFiles;
    COptionsForFreeSpace m_pgFreeSpace;

    LibrarySettings m_lsSettings;

// Operations
public:

// Overrides
    // ClassWizard generated virtual function overrides
    //{{AFX_VIRTUAL(COptionsDlg)
    //}}AFX_VIRTUAL

// Implementation
public:
    virtual ~COptionsDlg();

// Generated message map functions
protected:
    //{{AFX_MSG(COptionsDlg)
        // NOTE - the ClassWizard will add and remove member functions here.
    //}}AFX_MSG
    DECLARE_MESSAGE_MAP()
};

/////////////////////////////////////////////////////////////////////////////

#endif  // __OPTIONSDLG_H__
